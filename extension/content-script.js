// content-script.js - Main injection script for Google AI Studio integration
class CoderCompanionInjector {
  constructor() {
    this.sidebarContainer = null;
    this.toggleButton = null;
    this.isInjected = false;
    this.isSidebarOpen = false;
  }

  async init() {
    console.log('[Coder Companion] Initializing extension...');

    try {
      await this.waitForPageLoad();
      this.injectToggleButton();
      this.injectSidebar();
      this.setupEventListeners();
      this.loadExtensionState();

      // Initialize sample data if needed
      await this.initializeSampleDataIfNeeded();

      // Mark as initialized and set up message listener
      this.isInitialized = true;
      this.setupMessageListener();

      console.log('[Coder Companion] Extension initialized successfully');
    } catch (error) {
      console.error('[Coder Companion] Initialization failed:', error);
    }
  }

  waitForPageLoad() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
      }
    });
  }

  injectToggleButton() {
    console.log('[Coder Companion] Injecting toggle button...');

    this.toggleButton = document.createElement('button');
    this.toggleButton.id = 'cc-toggle-btn';
    // Remove Tailwind classes - CSS is handled by extension.css
    this.toggleButton.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 12h18M3 6h18M3 18h18"/>
      </svg>
    `;
    this.toggleButton.title = 'Toggle Coder Companion Sidebar';

    document.body.appendChild(this.toggleButton);
  }

  injectSidebar() {
    console.log('[Coder Companion] Injecting sidebar...');

    this.sidebarContainer = document.createElement('div');
    this.sidebarContainer.id = 'cc-sidebar';
    // Remove Tailwind classes - CSS is handled by extension.css

    // Load sidebar HTML from extension resources
    fetch(chrome.runtime.getURL('sidebar/index.html'))
      .then(response => response.text())
      .then(html => {
        console.log('[Coder Companion] Sidebar HTML loaded, length:', html.length);

        // Remove the script tag from HTML to prevent immediate execution
        const cleanHtml = html.replace(/<script[^>]*src="script\.js"[^>]*><\/script>/g, '');

        this.sidebarContainer.innerHTML = cleanHtml;
        document.body.appendChild(this.sidebarContainer);
        console.log('[Coder Companion] Sidebar added to DOM');

        // Now manually load and execute the sidebar script
        this.loadSidebarScript();

        this.initializeSidebarComponents();
        this.isInjected = true;
        console.log('[Coder Companion] Sidebar initialization complete');
      })
      .catch(error => {
        console.error('[Coder Companion] Failed to load sidebar:', error);
      });
  }


  setupEventListeners() {
    console.log('[Coder Companion] Setting up event listeners...');

    // Toggle button click
    this.toggleButton.addEventListener('click', () => {
      this.toggleSidebar();
    });

    // Keyboard shortcut (Ctrl+Shift+C)
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        this.toggleSidebar();
      }
    });

    // Close sidebar when clicking outside
    document.addEventListener('click', (event) => {
      if (this.isSidebarOpen &&
          !this.sidebarContainer.contains(event.target) &&
          !this.toggleButton.contains(event.target)) {
        this.closeSidebar();
      }
    });
  }

  toggleSidebar() {
    if (this.isSidebarOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    if (this.sidebarContainer && !this.isSidebarOpen) {
      console.log('[Coder Companion] Opening sidebar - current classes:', this.sidebarContainer.className);

      // Use CSS class instead of direct style manipulation
      this.sidebarContainer.classList.add('open');
      this.isSidebarOpen = true;

      console.log('[Coder Companion] Sidebar opened - new classes:', this.sidebarContainer.className);

      // Debug: Check sidebar position and visibility
      const rect = this.sidebarContainer.getBoundingClientRect();
      const computed = window.getComputedStyle(this.sidebarContainer);
      console.log('[Coder Companion] Sidebar position:', {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height,
        display: computed.display,
        visibility: computed.visibility,
        opacity: computed.opacity,
        zIndex: computed.zIndex,
        transform: computed.transform
      });

      // Update toggle button appearance - remove Tailwind classes and use a simple active state
      this.toggleButton.style.background = 'linear-gradient(135deg, #2563eb, #1e40af)';
      this.toggleButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 6L6 18M6 6l12 12"/>
        </svg>
      `;

      // Notify sidebar components
      this.dispatchSidebarEvent('sidebarOpened');
    }
  }

  closeSidebar() {
    if (this.sidebarContainer && this.isSidebarOpen) {
      // Use CSS class instead of direct style manipulation
      this.sidebarContainer.classList.remove('open');
      this.isSidebarOpen = false;

      // Update toggle button appearance - reset to default gradient
      this.toggleButton.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
      this.toggleButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 12h18M3 6h18M3 18h18"/>
        </svg>
      `;

      // Notify sidebar components
      this.dispatchSidebarEvent('sidebarClosed');
    }
  }

  dispatchSidebarEvent(eventType) {
    const event = new CustomEvent(eventType, {
      detail: { sidebar: this.sidebarContainer }
    });
    document.dispatchEvent(event);
  }

  loadExtensionState() {
    console.log('[Coder Companion] Loading extension state...');

    // Load saved preferences and state from storage
    chrome.storage.local.get(['sidebarOpen', 'lastActiveTab'], (result) => {
      if (result.sidebarOpen) {
        // Small delay to ensure DOM is ready, then open sidebar
        setTimeout(() => {
          this.openSidebar();
        }, 100);
      }
    });
  }

  loadSidebarScript() {
    console.log('[Coder Companion] Loading sidebar script...');

    // Instead of injecting inline script (which violates CSP),
    // we'll create the sidebar functionality directly in this content script
    this.initializeSidebarFunctionality();
  }

  initializeSidebarFunctionality() {
    console.log('[Coder Companion] Initializing sidebar functionality...');

    // Wait for DOM to be ready
    setTimeout(() => {
      this.createSidebarContent();
      this.setupSidebarEventListeners();
    }, 100);
  }

  createSidebarContent() {
    console.log('[Coder Companion] Creating sidebar content...');

    // Create each panel with specific content
    this.createProjectsPanel();
    this.createPersonasPanel();
    this.createArtifactsPanel();
    this.createExportPanel();
  }

  createProjectsPanel() {
    const tabElement = document.getElementById('projects-tab');
    console.log('[Coder Companion] Tab projects:', tabElement);

    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-projects-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Project
            </button>
            <button id="refresh-projects-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="project-search" class="cc-input" placeholder="Search projects...">
          </div>
        </div>
        <div class="panel-content">
          <div id="projects-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading projects...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="projects-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="projects-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Completed:</span>
              <span class="stat-value" id="projects-completed">0</span>
            </div>
          </div>
        </div>
      `;

      // Load data for projects
      setTimeout(() => this.loadTabData('projects'), 500);
      console.log('[Coder Companion] Created projects panel with search and stats');
    } else {
      console.error('[Coder Companion] Tab element not found: projects-tab');
    }
  }

  createPersonasPanel() {
    const tabElement = document.getElementById('personas-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-personas-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Persona
            </button>
            <button id="refresh-personas-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="persona-search" class="cc-input" placeholder="Search personas...">
          </div>
        </div>
        <div class="panel-content">
          <div id="personas-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading personas...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="personas-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="personas-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Completed:</span>
              <span class="stat-value" id="personas-completed">0</span>
            </div>
          </div>
        </div>
      `;
      setTimeout(() => this.loadTabData('personas'), 500);
      console.log('[Coder Companion] Created personas panel');
    }
  }

  createArtifactsPanel() {
    const tabElement = document.getElementById('artifacts-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-artifacts-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Artifact
            </button>
            <button id="refresh-artifacts-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="artifact-search" class="cc-input" placeholder="Search artifacts...">
          </div>
        </div>
        <div class="panel-content">
          <div id="artifacts-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading artifacts...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="artifacts-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="artifacts-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Completed:</span>
              <span class="stat-value" id="artifacts-completed">0</span>
            </div>
          </div>
        </div>
      `;
      setTimeout(() => this.loadTabData('artifacts'), 500);
      console.log('[Coder Companion] Created artifacts panel');
    }
  }

  createExportPanel() {
    const tabElement = document.getElementById('export-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-export-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              Export Data
            </button>
            <button id="refresh-export-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="panel-content">
          <div id="export-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading export options...
            </div>
          </div>
        </div>
      `;
      setTimeout(() => this.loadTabData('export'), 500);
      console.log('[Coder Companion] Created export panel');
    }
  }

  updateStats(tabName, data) {
    console.log(`[Coder Companion] Updating stats for ${tabName}:`, data.length, 'items');

    // Update total count
    const totalElement = document.getElementById(`${tabName}-total`);
    if (totalElement) {
      totalElement.textContent = data.length;
    }

    switch (tabName) {
      case 'projects':
        const activeProjects = data.filter(p => p.status === 'active').length;
        const completedProjects = data.filter(p => p.status === 'completed').length;

        const activeElement = document.getElementById('projects-active');
        const completedElement = document.getElementById('projects-completed');

        if (activeElement) activeElement.textContent = activeProjects;
        if (completedElement) completedElement.textContent = completedProjects;

        console.log(`[Coder Companion] Projects stats: Total=${data.length}, Active=${activeProjects}, Completed=${completedProjects}`);
        break;

      case 'personas':
        const activePersonas = data.filter(p => p.status === 'active').length;
        const inactivePersonas = data.filter(p => p.status !== 'active').length;

        const personasActiveElement = document.getElementById('personas-active');
        const personasCompletedElement = document.getElementById('personas-completed');

        if (personasActiveElement) personasActiveElement.textContent = activePersonas;
        if (personasCompletedElement) personasCompletedElement.textContent = inactivePersonas;

        console.log(`[Coder Companion] Personas stats: Total=${data.length}, Active=${activePersonas}, Inactive=${inactivePersonas}`);
        break;

      case 'artifacts':
        const completedArtifacts = data.filter(a => a.status === 'completed').length;
        const draftArtifacts = data.filter(a => a.status === 'draft').length;

        const artifactsActiveElement = document.getElementById('artifacts-active');
        const artifactsCompletedElement = document.getElementById('artifacts-completed');

        if (artifactsActiveElement) artifactsActiveElement.textContent = draftArtifacts;
        if (artifactsCompletedElement) artifactsCompletedElement.textContent = completedArtifacts;

        console.log(`[Coder Companion] Artifacts stats: Total=${data.length}, Draft=${draftArtifacts}, Completed=${completedArtifacts}`);
        break;
    }
  }

  setupSidebarEventListeners() {
    console.log('[Coder Companion] Setting up sidebar event listeners...');

    // Tab navigation
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        console.log(`[Coder Companion] Tab clicked: ${tabId}`);
        this.switchTab(tabId);
      });
    });

    // Action buttons
    const tabs = ['projects', 'personas', 'artifacts', 'export'];
    tabs.forEach(tabName => {
      const newBtn = document.getElementById(`new-${tabName}-btn`);
      const refreshBtn = document.getElementById(`refresh-${tabName}-btn`);

      if (newBtn) {
        newBtn.addEventListener('click', () => {
          console.log(`[Coder Companion] New ${tabName} clicked`);
          this.handleNewItem(tabName);
        });
      }

      if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
          console.log(`[Coder Companion] Refresh ${tabName} clicked`);
          this.loadTabData(tabName);
        });
      }
    });
  }

  switchTab(tabId) {
    console.log(`[Coder Companion] Switching to tab: ${tabId}`);

    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeButton) {
      activeButton.classList.add('active');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    const activeContent = document.getElementById(`${tabId}-tab`);
    if (activeContent) {
      activeContent.classList.add('active');
    }
  }

  async loadTabData(tabName) {
    console.log(`[Coder Companion] Loading data for ${tabName}`);

    const listElement = document.getElementById(`${tabName}-list`);
    if (!listElement) return;

    // Show loading state
    listElement.innerHTML = `
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading ${tabName}...
      </div>
    `;

    try {
      // Load data (real or sample)
      const data = await this.getSampleData(tabName);
      this.renderList(tabName, data);
      this.updateStats(tabName, data);
    } catch (error) {
      console.error(`[Coder Companion] Failed to load ${tabName} data:`, error);
      listElement.innerHTML = `
        <div class="empty-state">
          <h3>Failed to load data</h3>
          <p>Please try again later</p>
        </div>
      `;
    }
  }

  getSampleData(tabName) {
    switch (tabName) {
      case 'projects':
        return [
          { id: '1', name: 'AI Assistant Development', status: 'active', lastModified: '2 hours ago' },
          { id: '2', name: 'Code Review Tool', status: 'draft', lastModified: '1 day ago' }
        ];
      case 'personas':
        return [
          { id: '1', name: 'Senior Frontend Developer', status: 'active', lastModified: '30 minutes ago' },
          { id: '2', name: 'Backend Architect', status: 'active', lastModified: '2 hours ago' }
        ];
      case 'artifacts':
        return [
          { id: '1', name: 'API Documentation', status: 'completed', lastModified: '1 hour ago' },
          { id: '2', name: 'Database Schema', status: 'draft', lastModified: '3 hours ago' }
        ];
      case 'export':
        return [
          { id: '1', name: 'Current Project Export', status: 'ready', lastModified: 'Available now' },
          { id: '2', name: 'All Projects Archive', status: 'ready', lastModified: 'Available now' }
        ];
      default:
        return [];
    }
  }

  renderList(tabName, items) {
    const listElement = document.getElementById(`${tabName}-list`);
    if (!listElement) return;

    if (items.length === 0) {
      listElement.innerHTML = `
        <div class="empty-state">
          <h3>No ${tabName} found</h3>
          <p>Create your first ${tabName.slice(0, -1)} to get started</p>
        </div>
      `;
      return;
    }

    const itemsHtml = items.map(item => `
      <div class="cc-list-item" data-id="${item.id}">
        <div class="item-title">${item.name}</div>
        <div class="item-meta">
          <span class="status-indicator ${item.status}">${item.status}</span>
          <span>Last modified: ${item.lastModified}</span>
        </div>
      </div>
    `).join('');

    listElement.innerHTML = itemsHtml;

    // Add click handlers
    listElement.querySelectorAll('.cc-list-item').forEach(item => {
      item.addEventListener('click', () => {
        console.log(`[Coder Companion] Clicked ${tabName} item:`, item.dataset.id);
        this.handleItemClick(tabName, item.dataset.id);
      });
    });

    // Update stats
    const totalElement = document.getElementById(`${tabName}-total`);
    if (totalElement) {
      totalElement.textContent = items.length;
    }
  }

  handleNewItem(tabName) {
    const itemType = tabName.slice(0, -1); // Remove 's' from end
    console.log(`[Coder Companion] Creating new ${itemType}`);

    switch (tabName) {
      case 'projects':
        this.showCreateProjectModal();
        break;
      case 'personas':
        this.showCreatePersonaModal();
        break;
      case 'artifacts':
        this.showCreateArtifactModal();
        break;
      case 'export':
        this.showExportModal();
        break;
      default:
        alert(`Create New ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}\n\nModal not implemented yet.`);
    }
  }

  async handleItemClick(tabName, itemId) {
    const itemType = tabName.slice(0, -1);
    console.log(`[Coder Companion] Opening ${itemType}: ${itemId}`);

    try {
      // Get item data (await the async function)
      const items = await this.getSampleData(tabName);
      console.log(`[Coder Companion] Items for ${tabName}:`, items);

      // Ensure items is an array
      if (!Array.isArray(items)) {
        console.error(`[Coder Companion] Items is not an array:`, items);
        alert(`Failed to load ${itemType} data`);
        return;
      }

      const item = items.find(i => i.id === itemId);

      if (item) {
        this.showItemDetailsModal(itemType, item);
      } else {
        console.warn(`[Coder Companion] Item not found: ${itemId} in`, items);
        alert(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)} not found: ${itemId}`);
      }
    } catch (error) {
      console.error(`[Coder Companion] Failed to handle item click:`, error);
      alert(`Failed to open ${itemType} details`);
    }
  }

  // Modal System
  createModal(title, content, className = '') {
    // Remove existing modal if any
    const existingModal = document.getElementById('cc-modal');
    if (existingModal) {
      existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'cc-modal';
    modalOverlay.className = `cc-modal-overlay ${className}`;
    modalOverlay.innerHTML = `
      <div class="cc-modal">
        <div class="cc-modal-header">
          <h3>${title}</h3>
          <button class="cc-modal-close">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="cc-modal-content">
          ${content}
        </div>
      </div>
    `;

    // Add styles if not already added
    this.addModalStyles();

    document.body.appendChild(modalOverlay);

    // Setup event listeners
    this.setupModalEventListeners(modalOverlay);

    return modalOverlay;
  }

  setupModalEventListeners(modalOverlay) {
    // Close button
    const closeButton = modalOverlay.querySelector('.cc-modal-close');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        console.log('[Coder Companion] Modal close button clicked');
        modalOverlay.remove();
      });
    }

    // Cancel buttons
    const cancelButtons = modalOverlay.querySelectorAll('.cc-btn-secondary');
    cancelButtons.forEach(button => {
      if (button.textContent.trim() === 'Cancel') {
        button.addEventListener('click', () => {
          console.log('[Coder Companion] Modal cancel button clicked');
          modalOverlay.remove();
        });
      }
    });

    // Close on overlay click
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        console.log('[Coder Companion] Modal overlay clicked');
        modalOverlay.remove();
      }
    });

    // Export button
    const exportButton = modalOverlay.querySelector('#export-data-btn');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        console.log('[Coder Companion] Export data button clicked');
        this.handleExport();
      });
    }

    // Edit button is handled in showItemDetailsModal if present

    // Close buttons (Close, not Cancel)
    const closeButtons = modalOverlay.querySelectorAll('.cc-btn-secondary');
    closeButtons.forEach(button => {
      if (button.textContent.trim() === 'Close') {
        button.addEventListener('click', () => {
          console.log('[Coder Companion] Modal close button clicked');
          modalOverlay.remove();
        });
      }
    });

    // Close on Escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        console.log('[Coder Companion] Escape key pressed');
        modalOverlay.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
  }

  addModalStyles() {
    if (document.getElementById('cc-modal-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'cc-modal-styles';
    styles.textContent = `
      .cc-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
      }

      .cc-modal {
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        max-width: 500px;
        width: 100%;
        max-height: 90vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      .cc-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;
      }

      .cc-modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #111827;
      }

      .cc-modal-close {
        background: none;
        border: none;
        padding: 4px;
        cursor: pointer;
        color: #6b7280;
        border-radius: 4px;
        transition: all 0.2s ease;
      }

      .cc-modal-close:hover {
        background: #e5e7eb;
        color: #374151;
      }

      .cc-modal-content {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
      }

      .cc-form {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .cc-form-group {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .cc-form-group label {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
      }

      .cc-form-group input,
      .cc-form-group textarea,
      .cc-form-group select {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease;
      }

      .cc-form-group input:focus,
      .cc-form-group textarea:focus,
      .cc-form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .cc-form-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-top: 8px;
        padding-top: 16px;
        border-top: 1px solid #e5e7eb;
      }

      .cc-btn {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
      }

      .cc-btn-primary {
        background: #3b82f6;
        color: white;
      }

      .cc-btn-primary:hover {
        background: #2563eb;
      }

      .cc-btn-secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
      }

      .cc-btn-secondary:hover {
        background: #e5e7eb;
      }

      .cc-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .cc-item-details {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .cc-detail-group {
        display: flex;
        gap: 12px;
        align-items: flex-start;
      }

      .cc-detail-group label {
        font-weight: 600;
        color: #374151;
        min-width: 100px;
        font-size: 14px;
      }

      .cc-detail-group span {
        color: #6b7280;
        font-size: 14px;
      }

      .cc-status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        text-transform: uppercase;
      }

      .cc-status-badge.active {
        background-color: #dcfce7;
        color: #166534;
      }

      .cc-status-badge.draft {
        background-color: #fef3c7;
        color: #92400e;
      }

      .cc-status-badge.completed {
        background-color: #dbeafe;
        color: #1e40af;
      }

      .cc-status-badge.ready {
        background-color: #e0e7ff;
        color: #3730a3;
      }
    `;

    document.head.appendChild(styles);
  }

  // Project Modal
  showCreateProjectModal() {
    const modal = this.createModal('Create New Project', `
      <form class="cc-form" id="create-project-form">
        <div class="cc-form-group">
          <label for="project-name">Project Name *</label>
          <input type="text" id="project-name" required placeholder="Enter project name">
        </div>
        <div class="cc-form-group">
          <label for="project-description">Description</label>
          <textarea id="project-description" rows="3" placeholder="Describe your project..."></textarea>
        </div>
        <div class="cc-form-group">
          <label for="project-priority">Priority</label>
          <select id="project-priority">
            <option value="low">Low</option>
            <option value="medium" selected>Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="project-tags">Tags (comma-separated)</label>
          <input type="text" id="project-tags" placeholder="web, frontend, react">
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Create Project</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-project-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleCreateProject(form, modal);
    });
  }

  showCreatePersonaModal() {
    const modal = this.createModal('Create New Persona', `
      <form class="cc-form" id="create-persona-form">
        <div class="cc-form-group">
          <label for="persona-name">Persona Name *</label>
          <input type="text" id="persona-name" required placeholder="e.g., Senior Frontend Developer">
        </div>
        <div class="cc-form-group">
          <label for="persona-description">Description</label>
          <textarea id="persona-description" rows="2" placeholder="Brief description of this persona..."></textarea>
        </div>
        <div class="cc-form-group">
          <label for="persona-role">Role</label>
          <select id="persona-role">
            <option value="assistant">Assistant</option>
            <option value="expert">Expert</option>
            <option value="reviewer">Reviewer</option>
            <option value="mentor">Mentor</option>
            <option value="analyst">Analyst</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="persona-personality">Personality</label>
          <select id="persona-personality">
            <option value="professional">Professional</option>
            <option value="friendly">Friendly</option>
            <option value="technical">Technical</option>
            <option value="creative">Creative</option>
            <option value="concise">Concise</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="persona-expertise">Expertise (comma-separated)</label>
          <input type="text" id="persona-expertise" placeholder="React, TypeScript, Node.js">
        </div>
        <div class="cc-form-group">
          <label for="persona-system-prompt">System Prompt</label>
          <textarea id="persona-system-prompt" rows="3" placeholder="You are a helpful assistant specialized in..."></textarea>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Create Persona</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-persona-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleCreatePersona(form, modal);
    });
  }

  showCreateArtifactModal() {
    const modal = this.createModal('Create New Artifact', `
      <form class="cc-form" id="create-artifact-form">
        <div class="cc-form-group">
          <label for="artifact-name">Artifact Name *</label>
          <input type="text" id="artifact-name" required placeholder="Enter artifact name">
        </div>
        <div class="cc-form-group">
          <label for="artifact-type">Type</label>
          <select id="artifact-type">
            <option value="document">Document</option>
            <option value="code">Code</option>
            <option value="template">Template</option>
            <option value="specification">Specification</option>
            <option value="guide">Guide</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="artifact-content">Content</label>
          <textarea id="artifact-content" rows="6" placeholder="Enter the artifact content..."></textarea>
        </div>
        <div class="cc-form-group">
          <label for="artifact-tags">Tags (comma-separated)</label>
          <input type="text" id="artifact-tags" placeholder="documentation, api, guide">
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Create Artifact</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-artifact-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleCreateArtifact(form, modal);
    });
  }

  showExportModal() {
    const modal = this.createModal('Export Data', `
      <div class="cc-form">
        <div class="cc-form-group">
          <label>Export Options</label>
          <div style="display: flex; flex-direction: column; gap: 12px; margin-top: 8px;">
            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
              <input type="checkbox" id="export-projects" checked>
              Export Projects
            </label>
            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
              <input type="checkbox" id="export-personas" checked>
              Export Personas
            </label>
            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
              <input type="checkbox" id="export-artifacts" checked>
              Export Artifacts
            </label>
          </div>
        </div>
        <div class="cc-form-group">
          <label for="export-format">Export Format</label>
          <select id="export-format">
            <option value="json">JSON</option>
            <option value="markdown">Markdown</option>
            <option value="csv">CSV</option>
          </select>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="button" class="cc-btn cc-btn-primary" id="export-data-btn">Export Data</button>
        </div>
      </div>
    `);
  }

  showItemDetailsModal(itemType, item) {
    // Check if this item type is editable
    const isEditable = ['project', 'persona', 'artifact'].includes(itemType);

    // Create edit button HTML only for editable items
    const editButtonHtml = isEditable
      ? `<button type="button" class="cc-btn cc-btn-primary" id="edit-item-btn">Edit ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}</button>`
      : '';

    const modal = this.createModal(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)}: ${item.name}`, `
      <div class="cc-item-details">
        <div class="cc-detail-group">
          <label>Name:</label>
          <span>${item.name}</span>
        </div>
        <div class="cc-detail-group">
          <label>Status:</label>
          <span class="cc-status-badge ${item.status}">${item.status}</span>
        </div>
        <div class="cc-detail-group">
          <label>Last Modified:</label>
          <span>${item.lastModified}</span>
        </div>
        <div class="cc-detail-group">
          <label>ID:</label>
          <span>${item.id}</span>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Close</button>
          ${editButtonHtml}
        </div>
      </div>
    `);

    // Store item data in the modal for later use
    modal.setAttribute('data-item-type', itemType);
    modal.setAttribute('data-item-id', item.id);
    modal.setAttribute('data-item-data', JSON.stringify(item));

    // Setup specific event listener for edit button (only if it exists)
    const editButton = modal.querySelector('#edit-item-btn');
    if (editButton) {
      editButton.addEventListener('click', () => {
        console.log('[Coder Companion] Edit item button clicked');
        this.handleEditItem(item, itemType);
        modal.remove();
      });
    } else {
      console.log(`[Coder Companion] No edit button for ${itemType} (not editable)`);
    }
  }

  handleEditItem(item, itemType) {
    console.log(`[Coder Companion] Handling edit for ${itemType}:`, item);

    // TEMPORARY: Always open edit modal directly for debugging
    console.log('[Coder Companion] DEBUG: Opening edit modal directly');
    try {
      this.openEditModalDirectly(itemType, item.id, item);
    } catch (error) {
      console.error('[Coder Companion] Error opening edit modal:', error);
      // Fallback to instructions
      this.showEditInstructions(itemType, item);
    }

    /* ORIGINAL LOGIC (commented out for debugging)
    // Check if sidebar is already open and visible
    const sidebar = document.querySelector('#coder-companion-sidebar');
    const isVisible = sidebar &&
                     sidebar.style.display !== 'none' &&
                     sidebar.style.visibility !== 'hidden' &&
                     !sidebar.classList.contains('hidden');

    if (isVisible) {
      console.log('[Coder Companion] Sidebar is open, communicating directly');
      this.communicateWithSidebar(itemType, item.id, item);
    } else {
      console.log('[Coder Companion] Sidebar not open, showing instructions');
      this.showEditInstructions(itemType, item);
    }
    */
  }

  openSidebarWithEdit(itemType, itemId, item = null, retryCount = 0) {
    console.log(`[Coder Companion] Opening sidebar with edit for ${itemType}:`, itemId, `(retry: ${retryCount})`);

    // Prevent infinite loops
    if (retryCount > 2) {
      console.log('[Coder Companion] Max retries reached, showing fallback message');
      this.showEditFallbackMessage(itemType, itemId);
      return;
    }

    try {
      const sidebar = document.querySelector('#coder-companion-sidebar');

      // If sidebar doesn't exist and this is the first try, try to create it
      if (!sidebar && retryCount === 0) {
        console.log('[Coder Companion] Sidebar not found, trying to open it first');
        this.toggleSidebar();

        // Wait for sidebar to be created, then try again (only once)
        setTimeout(() => {
          this.openSidebarWithEdit(itemType, itemId, item, retryCount + 1);
        }, 1000);
        return;
      }

      // If sidebar still doesn't exist after retry, give up
      if (!sidebar) {
        console.log('[Coder Companion] Sidebar still not found after retry');
        this.showEditFallbackMessage(itemType, itemId);
        return;
      }

      // Check if sidebar is visible
      const isVisible = sidebar.style.display !== 'none' &&
                       sidebar.style.visibility !== 'hidden' &&
                       !sidebar.classList.contains('hidden');

      // If not visible and this is first try, try to show it
      if (!isVisible && retryCount === 0) {
        console.log('[Coder Companion] Sidebar not visible, showing it first');
        this.toggleSidebar();

        // Wait for sidebar to become visible, then try again (only once)
        setTimeout(() => {
          this.openSidebarWithEdit(itemType, itemId, item, retryCount + 1);
        }, 500);
        return;
      }

      // At this point, either sidebar is visible or we've tried to make it visible
      // Try to communicate regardless
      console.log('[Coder Companion] Attempting to communicate with sidebar');
      this.communicateWithSidebar(itemType, itemId, item);

    } catch (error) {
      console.error('[Coder Companion] Failed to open sidebar with edit:', error);
      this.showEditFallbackMessage(itemType, itemId);
    }
  }

  communicateWithSidebar(itemType, itemId, item) {
    console.log('[Coder Companion] Attempting to communicate with sidebar');

    // Since the sidebar is loaded directly in the DOM (not iframe),
    // we can call the edit methods directly
    try {
      this.openEditModalDirectly(itemType, itemId, item);
    } catch (error) {
      console.log('[Coder Companion] Direct edit failed, trying other methods:', error);

      // Fallback: Try postMessage and runtime message
      this.tryPostMessage(itemType, itemId, item);

      setTimeout(() => {
        try {
          chrome.runtime.sendMessage({
            type: 'SIDEBAR_ACTION',
            action: 'edit',
            itemType: itemType,
            itemId: itemId,
            itemData: item
          }, (response) => {
            if (chrome.runtime.lastError) {
              console.log('[Coder Companion] Runtime message failed:', chrome.runtime.lastError.message);
            } else {
              console.log('[Coder Companion] Runtime message sent successfully');
            }
          });
        } catch (error) {
          console.log('[Coder Companion] Runtime message error:', error);
        }
      }, 100);
    }
  }

  openEditModalDirectly(itemType, itemId, item) {
    console.log(`[Coder Companion] Opening edit modal directly for ${itemType}:`, itemId);

    // Check for non-editable types
    if (itemType === 'export' || itemType === 'expor') {
      console.log('[Coder Companion] Export is not editable, showing info instead');
      this.showExportInfo();
      return;
    }

    // SIMPLIFIED: Open modal immediately without tab switching
    switch (itemType) {
      case 'project':
        console.log('[Coder Companion] Opening project edit modal...');
        this.showEditProjectModal(itemId, item);
        break;
      case 'persona':
        console.log('[Coder Companion] Opening persona edit modal...');
        this.showEditPersonaModal(itemId, item);
        break;
      case 'artifact':
        console.log('[Coder Companion] Opening artifact edit modal...');
        this.showEditArtifactModal(itemId, item);
        break;
      default:
        console.error('[Coder Companion] Unknown item type for edit:', itemType);
        this.showUnsupportedEditMessage(itemType);
    }
  }

  switchToTab(itemType) {
    const tabName = itemType === 'project' ? 'projects' :
                   itemType === 'persona' ? 'personas' : 'artifacts';

    console.log(`[Coder Companion] Switching to ${tabName} tab`);

    // Find and click the appropriate tab
    const tabButton = document.querySelector(`[data-tab="${tabName}"]`);
    if (tabButton) {
      // Remove active class from all tabs
      document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));

      // Add active class to clicked tab
      tabButton.classList.add('active');

      // Load the tab data
      this.loadTabData(tabName);
    } else {
      console.error(`[Coder Companion] Tab button not found for: ${tabName}`);
    }
  }

  tryPostMessage(itemType, itemId, item) {
    // Method 2: Try postMessage to iframe
    const sidebarFrame = document.querySelector('#coder-companion-sidebar iframe');
    if (sidebarFrame && sidebarFrame.contentWindow) {
      console.log('[Coder Companion] Sending postMessage to sidebar iframe');
      sidebarFrame.contentWindow.postMessage({
        type: 'EDIT_ITEM',
        itemType: itemType,
        itemId: itemId,
        itemData: item
      }, '*');

      // Also try to switch to the appropriate tab
      setTimeout(() => {
        const tabId = itemType === 'project' ? 'projects' :
                     itemType === 'persona' ? 'personas' : 'artifacts';
        sidebarFrame.contentWindow.postMessage({
          type: 'SWITCH_TAB',
          tabId: tabId
        }, '*');
      }, 100);

    } else {
      console.log('[Coder Companion] Sidebar iframe not found, trying direct window message');
      // Method 3: Try direct window postMessage
      window.postMessage({
        type: 'EDIT_ITEM',
        itemType: itemType,
        itemId: itemId,
        itemData: item,
        source: 'content-script'
      }, '*');
    }
  }

  showEditInstructions(itemType, item) {
    const itemName = item.name || item.title || `${itemType} ${item.id}`;
    const tabName = itemType === 'project' ? 'Projects' :
                   itemType === 'persona' ? 'Personas' : 'Artifacts';

    const modal = this.createModal(`Edit ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`, `
      <div class="cc-edit-instructions">
        <div class="cc-instruction-header">
          <h3>Ready to edit: ${itemName}</h3>
          <p>Please follow these steps to edit this ${itemType}:</p>
        </div>
        <div class="cc-instruction-steps">
          <div class="cc-step">
            <span class="cc-step-number">1</span>
            <div class="cc-step-content">
              <strong>Open the sidebar</strong>
              <p>Click the extension icon to open the sidebar</p>
            </div>
          </div>
          <div class="cc-step">
            <span class="cc-step-number">2</span>
            <div class="cc-step-content">
              <strong>Go to ${tabName} tab</strong>
              <p>Click on the "${tabName}" tab in the sidebar</p>
            </div>
          </div>
          <div class="cc-step">
            <span class="cc-step-number">3</span>
            <div class="cc-step-content">
              <strong>Find and edit</strong>
              <p>Look for "${itemName}" and click on it to edit</p>
            </div>
          </div>
        </div>
        <div class="cc-instruction-footer">
          <button type="button" class="cc-btn cc-btn-primary" onclick="this.closest('.cc-modal-overlay').remove()">Got it!</button>
        </div>
      </div>
    `);
  }

  showExportInfo() {
    const modal = this.createModal('Export Information', `
      <div class="cc-export-info">
        <div class="cc-info-header">
          <h3>About Export Functionality</h3>
          <p>Export allows you to download your data in various formats.</p>
        </div>
        <div class="cc-info-content">
          <div class="cc-info-section">
            <h4>Available Export Options:</h4>
            <ul>
              <li><strong>JSON</strong> - Complete data with all metadata</li>
              <li><strong>CSV</strong> - Spreadsheet-compatible format</li>
              <li><strong>Markdown</strong> - Human-readable documentation</li>
            </ul>
          </div>
          <div class="cc-info-section">
            <h4>How to Export:</h4>
            <ol>
              <li>Click on "Export" in the sidebar</li>
              <li>Choose your preferred format</li>
              <li>Select which data to include</li>
              <li>Click "Download" to save the file</li>
            </ol>
          </div>
        </div>
        <div class="cc-info-footer">
          <button type="button" class="cc-btn cc-btn-primary" onclick="this.closest('.cc-modal-overlay').remove()">Got it!</button>
        </div>
      </div>
    `);
  }

  showUnsupportedEditMessage(itemType) {
    const modal = this.createModal('Edit Not Available', `
      <div class="cc-unsupported-edit">
        <div class="cc-info-header">
          <h3>Cannot Edit ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}</h3>
          <p>This type of item cannot be edited directly.</p>
        </div>
        <div class="cc-info-content">
          <p>The "${itemType}" item is not a data object that can be modified. It represents an action or system function.</p>
          <p>If you need to modify settings or data, please look for the appropriate options in the sidebar.</p>
        </div>
        <div class="cc-info-footer">
          <button type="button" class="cc-btn cc-btn-primary" onclick="this.closest('.cc-modal-overlay').remove()">Understood</button>
        </div>
      </div>
    `);
  }

  showEditFallbackMessage(itemType, itemId) {
    const message = `Unable to automatically open the ${itemType} editor.\n\n` +
                   `Please:\n` +
                   `1. Open the sidebar manually\n` +
                   `2. Go to the ${itemType}s tab\n` +
                   `3. Find and edit the ${itemType} with ID: ${itemId}`;
    alert(message);
  }

  // Edit Modal Methods
  showEditProjectModal(itemId, item) {
    console.log(`[Coder Companion] Showing edit project modal for:`, itemId, item);

    // For now, show a simple edit modal with basic fields
    const modal = this.createModal(`Edit Project: ${item.name}`, `
      <form id="edit-project-form" class="cc-form">
        <div class="cc-form-group">
          <label for="edit-project-name">Project Name *</label>
          <input type="text" id="edit-project-name" class="cc-input" required value="${item.name || ''}">
        </div>
        <div class="cc-form-group">
          <label for="edit-project-description">Description</label>
          <textarea id="edit-project-description" class="cc-textarea" rows="3">${item.description || ''}</textarea>
        </div>
        <div class="cc-form-group">
          <label for="edit-project-status">Status</label>
          <select id="edit-project-status" class="cc-select">
            <option value="active" ${item.status === 'active' ? 'selected' : ''}>Active</option>
            <option value="draft" ${item.status === 'draft' ? 'selected' : ''}>Draft</option>
            <option value="completed" ${item.status === 'completed' ? 'selected' : ''}>Completed</option>
            <option value="archived" ${item.status === 'archived' ? 'selected' : ''}>Archived</option>
          </select>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary" onclick="this.closest('.cc-modal-overlay').remove()">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Update Project</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#edit-project-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleEditProjectSubmit(itemId, form, modal);
    });
  }

  showEditPersonaModal(itemId, item) {
    console.log(`[Coder Companion] Showing edit persona modal for:`, itemId, item);

    const modal = this.createModal(`Edit Persona: ${item.name}`, `
      <form id="edit-persona-form" class="cc-form">
        <div class="cc-form-group">
          <label for="edit-persona-name">Persona Name *</label>
          <input type="text" id="edit-persona-name" class="cc-input" required value="${item.name || ''}">
        </div>
        <div class="cc-form-group">
          <label for="edit-persona-description">Description</label>
          <textarea id="edit-persona-description" class="cc-textarea" rows="3">${item.description || ''}</textarea>
        </div>
        <div class="cc-form-group">
          <label for="edit-persona-role">Role</label>
          <select id="edit-persona-role" class="cc-select">
            <option value="assistant" ${item.role === 'assistant' ? 'selected' : ''}>Assistant</option>
            <option value="developer" ${item.role === 'developer' ? 'selected' : ''}>Developer</option>
            <option value="designer" ${item.role === 'designer' ? 'selected' : ''}>Designer</option>
            <option value="analyst" ${item.role === 'analyst' ? 'selected' : ''}>Analyst</option>
          </select>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary" onclick="this.closest('.cc-modal-overlay').remove()">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Update Persona</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#edit-persona-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleEditPersonaSubmit(itemId, form, modal);
    });
  }

  showEditArtifactModal(itemId, item) {
    console.log(`[Coder Companion] Showing edit artifact modal for:`, itemId, item);

    const modal = this.createModal(`Edit Artifact: ${item.name}`, `
      <form id="edit-artifact-form" class="cc-form">
        <div class="cc-form-group">
          <label for="edit-artifact-name">Artifact Name *</label>
          <input type="text" id="edit-artifact-name" class="cc-input" required value="${item.name || ''}">
        </div>
        <div class="cc-form-group">
          <label for="edit-artifact-description">Description</label>
          <textarea id="edit-artifact-description" class="cc-textarea" rows="3">${item.description || ''}</textarea>
        </div>
        <div class="cc-form-group">
          <label for="edit-artifact-content">Content</label>
          <textarea id="edit-artifact-content" class="cc-textarea" rows="8">${item.content || ''}</textarea>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary" onclick="this.closest('.cc-modal-overlay').remove()">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Update Artifact</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#edit-artifact-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleEditArtifactSubmit(itemId, form, modal);
    });
  }

  // Edit Form Handlers
  async handleEditProjectSubmit(itemId, form, modal) {
    console.log(`[Coder Companion] Handling edit project submit for:`, itemId);

    try {
      const formData = new FormData(form);
      const updates = {
        name: formData.get('edit-project-name') || form.querySelector('#edit-project-name').value,
        description: formData.get('edit-project-description') || form.querySelector('#edit-project-description').value,
        status: formData.get('edit-project-status') || form.querySelector('#edit-project-status').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Updating...';
      submitBtn.disabled = true;

      // Update project (for now, just simulate success)
      console.log('[Coder Companion] Project updates:', updates);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Close modal
      modal.remove();

      // Show success message
      this.showSuccessMessage(`Project "${updates.name}" updated successfully!`);

      // Refresh the projects list
      this.loadTabData('projects');

    } catch (error) {
      console.error('[Coder Companion] Failed to update project:', error);
      this.showErrorMessage('Failed to update project. Please try again.');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Update Project';
      submitBtn.disabled = false;
    }
  }

  async handleEditPersonaSubmit(itemId, form, modal) {
    console.log(`[Coder Companion] Handling edit persona submit for:`, itemId);

    try {
      const formData = new FormData(form);
      const updates = {
        name: formData.get('edit-persona-name') || form.querySelector('#edit-persona-name').value,
        description: formData.get('edit-persona-description') || form.querySelector('#edit-persona-description').value,
        role: formData.get('edit-persona-role') || form.querySelector('#edit-persona-role').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Updating...';
      submitBtn.disabled = true;

      // Update persona (for now, just simulate success)
      console.log('[Coder Companion] Persona updates:', updates);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Close modal
      modal.remove();

      // Show success message
      this.showSuccessMessage(`Persona "${updates.name}" updated successfully!`);

      // Refresh the personas list
      this.loadTabData('personas');

    } catch (error) {
      console.error('[Coder Companion] Failed to update persona:', error);
      this.showErrorMessage('Failed to update persona. Please try again.');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Update Persona';
      submitBtn.disabled = false;
    }
  }

  async handleEditArtifactSubmit(itemId, form, modal) {
    console.log(`[Coder Companion] Handling edit artifact submit for:`, itemId);

    try {
      const formData = new FormData(form);
      const updates = {
        name: formData.get('edit-artifact-name') || form.querySelector('#edit-artifact-name').value,
        description: formData.get('edit-artifact-description') || form.querySelector('#edit-artifact-description').value,
        content: formData.get('edit-artifact-content') || form.querySelector('#edit-artifact-content').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Updating...';
      submitBtn.disabled = true;

      // Update artifact (for now, just simulate success)
      console.log('[Coder Companion] Artifact updates:', updates);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Close modal
      modal.remove();

      // Show success message
      this.showSuccessMessage(`Artifact "${updates.name}" updated successfully!`);

      // Refresh the artifacts list
      this.loadTabData('artifacts');

    } catch (error) {
      console.error('[Coder Companion] Failed to update artifact:', error);
      this.showErrorMessage('Failed to update artifact. Please try again.');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Update Artifact';
      submitBtn.disabled = false;
    }
  }

  // Create Form Handlers
  async handleCreateProject(form, modal) {
    try {
      const formData = {
        name: form.querySelector('#project-name').value,
        description: form.querySelector('#project-description').value,
        priority: form.querySelector('#project-priority').value,
        tags: form.querySelector('#project-tags').value
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0)
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create project
      const project = await this.createProject(formData);

      // Close modal
      modal.remove();

      // Refresh projects list
      this.loadTabData('projects');

      // Show success notification
      this.showNotification('Project created successfully!', 'success');

    } catch (error) {
      console.error('[Coder Companion] Failed to create project:', error);
      this.showNotification('Failed to create project. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Project';
      submitBtn.disabled = false;
    }
  }

  async handleCreatePersona(form, modal) {
    try {
      const formData = {
        name: form.querySelector('#persona-name').value,
        description: form.querySelector('#persona-description').value,
        role: form.querySelector('#persona-role').value,
        personality: form.querySelector('#persona-personality').value,
        expertise: form.querySelector('#persona-expertise').value
          .split(',')
          .map(skill => skill.trim())
          .filter(skill => skill.length > 0),
        systemPrompt: form.querySelector('#persona-system-prompt').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create persona
      const persona = await this.createPersona(formData);

      // Close modal
      modal.remove();

      // Refresh personas list
      this.loadTabData('personas');

      // Show success notification
      this.showNotification('Persona created successfully!', 'success');

    } catch (error) {
      console.error('[Coder Companion] Failed to create persona:', error);
      this.showNotification('Failed to create persona. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Persona';
      submitBtn.disabled = false;
    }
  }

  async handleCreateArtifact(form, modal) {
    try {
      const formData = {
        name: form.querySelector('#artifact-name').value,
        type: form.querySelector('#artifact-type').value,
        content: form.querySelector('#artifact-content').value,
        tags: form.querySelector('#artifact-tags').value
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0)
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create artifact
      const artifact = await this.createArtifact(formData);

      // Close modal
      modal.remove();

      // Refresh artifacts list
      this.loadTabData('artifacts');

      // Show success notification
      this.showNotification('Artifact created successfully!', 'success');

    } catch (error) {
      console.error('[Coder Companion] Failed to create artifact:', error);
      this.showNotification('Failed to create artifact. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Artifact';
      submitBtn.disabled = false;
    }
  }

  handleExport() {
    const exportProjects = document.getElementById('export-projects')?.checked || false;
    const exportPersonas = document.getElementById('export-personas')?.checked || false;
    const exportArtifacts = document.getElementById('export-artifacts')?.checked || false;
    const format = document.getElementById('export-format')?.value || 'json';

    console.log('[Coder Companion] Exporting data:', {
      projects: exportProjects,
      personas: exportPersonas,
      artifacts: exportArtifacts,
      format: format
    });

    this.performExport({
      projects: exportProjects,
      personas: exportPersonas,
      artifacts: exportArtifacts,
      format: format
    });

    // Close modal
    const modal = document.getElementById('cc-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Utility Methods
  showSuccessMessage(message) {
    console.log('[Coder Companion] Success:', message);

    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = 'cc-toast cc-toast-success';
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      background: #10b981 !important;
      color: white !important;
      padding: 12px 20px !important;
      border-radius: 6px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      z-index: 10001 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      font-size: 14px !important;
      max-width: 300px !important;
      word-wrap: break-word !important;
    `;

    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);
  }

  showErrorMessage(message) {
    console.error('[Coder Companion] Error:', message);

    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = 'cc-toast cc-toast-error';
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      background: #ef4444 !important;
      color: white !important;
      padding: 12px 20px !important;
      border-radius: 6px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      z-index: 10001 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      font-size: 14px !important;
      max-width: 300px !important;
      word-wrap: break-word !important;
    `;

    document.body.appendChild(toast);

    // Remove after 4 seconds (longer for errors)
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 4000);
  }

  // Data Management
  generateId(prefix = 'item') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async createProject(data) {
    const projects = await this.getStoredData('projects', []);

    const project = {
      id: this.generateId('proj'),
      name: data.name,
      description: data.description || '',
      priority: data.priority || 'medium',
      tags: data.tags || [],
      status: 'active',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };

    projects.push(project);
    await this.setStoredData('projects', projects);

    console.log('[Coder Companion] Created project:', project.name);
    return project;
  }

  async createPersona(data) {
    const personas = await this.getStoredData('personas', []);

    const persona = {
      id: this.generateId('persona'),
      name: data.name,
      description: data.description || '',
      role: data.role || 'assistant',
      personality: data.personality || 'professional',
      expertise: data.expertise || [],
      systemPrompt: data.systemPrompt || '',
      status: 'active',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      usageCount: 0
    };

    personas.push(persona);
    await this.setStoredData('personas', personas);

    console.log('[Coder Companion] Created persona:', persona.name);
    return persona;
  }

  async createArtifact(data) {
    const artifacts = await this.getStoredData('artifacts', []);

    const artifact = {
      id: this.generateId('artifact'),
      name: data.name,
      type: data.type || 'document',
      content: data.content || '',
      tags: data.tags || [],
      status: 'draft',
      wordCount: this.countWords(data.content || ''),
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };

    artifacts.push(artifact);
    await this.setStoredData('artifacts', artifacts);

    console.log('[Coder Companion] Created artifact:', artifact.name);
    return artifact;
  }

  countWords(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  async initializeSampleDataIfNeeded() {
    try {
      console.log('[Coder Companion] Checking if sample data initialization is needed...');

      // Check if we already have data
      const projects = await this.getStoredData('projects', []);
      const personas = await this.getStoredData('personas', []);
      const artifacts = await this.getStoredData('artifacts', []);

      console.log('[Coder Companion] Current data counts:', {
        projects: projects.length,
        personas: personas.length,
        artifacts: artifacts.length
      });

      // If no data exists OR if we only have basic test data, create rich sample data
      const hasOnlyBasicData = projects.length === 1 && projects[0].name === 'test' && personas.length === 0 && artifacts.length === 0;

      if ((projects.length === 0 && personas.length === 0 && artifacts.length === 0) || hasOnlyBasicData) {
        if (hasOnlyBasicData) {
          console.log('[Coder Companion] Found basic test data, replacing with rich sample data...');
        }
        console.log('[Coder Companion] No data found, initializing rich sample data...');

        // Create rich sample projects
        const sampleProjects = [
          {
            id: this.generateId('proj'),
            name: 'AI Assistant Development',
            title: 'AI Assistant Development',
            description: 'Building a comprehensive AI assistant for developers',
            priority: 'high',
            tags: ['ai', 'development', 'assistant'],
            status: 'active',
            createdAt: new Date().toISOString(),
            lastModified: '2 hours ago',
            artifactCount: 15
          },
          {
            id: this.generateId('proj'),
            name: 'Code Review Tool',
            title: 'Code Review Tool',
            description: 'Automated code review and optimization suggestions',
            priority: 'medium',
            tags: ['code-review', 'automation'],
            status: 'draft',
            createdAt: new Date().toISOString(),
            lastModified: '1 day ago',
            artifactCount: 8
          }
        ];

        // Create rich sample personas
        const samplePersonas = [
          {
            id: this.generateId('persona'),
            name: 'Senior Frontend Developer',
            title: 'Senior Frontend Developer',
            description: 'Expert in React, TypeScript, and modern web development',
            role: 'developer',
            personality: 'professional',
            expertise: ['React', 'TypeScript', 'CSS', 'JavaScript'],
            systemPrompt: 'You are a senior frontend developer with expertise in modern web technologies.',
            status: 'active',
            createdAt: new Date().toISOString(),
            lastModified: '30 minutes ago',
            usageCount: 0
          },
          {
            id: this.generateId('persona'),
            name: 'Backend Architect',
            title: 'Backend Architect',
            description: 'Specializes in scalable systems and API design',
            role: 'architect',
            personality: 'analytical',
            expertise: ['Node.js', 'Python', 'Database Design', 'API Architecture'],
            systemPrompt: 'You are a backend architect focused on scalable system design.',
            status: 'active',
            createdAt: new Date().toISOString(),
            lastModified: '2 hours ago',
            usageCount: 0
          }
        ];

        // Create rich sample artifacts
        const sampleArtifacts = [
          {
            id: this.generateId('artifact'),
            name: 'API Documentation',
            title: 'API Documentation',
            description: 'Comprehensive API documentation with examples',
            type: 'documentation',
            content: 'This is a comprehensive API documentation...',
            tags: ['api', 'documentation', 'examples'],
            status: 'completed',
            wordCount: 1250,
            versions: 3,
            createdAt: new Date().toISOString(),
            lastModified: '1 hour ago'
          },
          {
            id: this.generateId('artifact'),
            name: 'Database Schema',
            title: 'Database Schema',
            description: 'Database design and migration scripts',
            type: 'code',
            content: 'CREATE TABLE users (...)',
            tags: ['database', 'schema', 'migration'],
            status: 'draft',
            wordCount: 450,
            versions: 1,
            createdAt: new Date().toISOString(),
            lastModified: '3 hours ago'
          }
        ];

        // Store the sample data
        await this.setStoredData('projects', sampleProjects);
        await this.setStoredData('personas', samplePersonas);
        await this.setStoredData('artifacts', sampleArtifacts);

        console.log('[Coder Companion] Rich sample data initialized successfully!');
        console.log('[Coder Companion] Created:', {
          projects: sampleProjects.length,
          personas: samplePersonas.length,
          artifacts: sampleArtifacts.length
        });
      } else {
        console.log('[Coder Companion] Data already exists, skipping sample data initialization');
      }
    } catch (error) {
      console.error('[Coder Companion] Failed to initialize sample data:', error);
    }
  }

  async getStoredData(key, defaultValue = null) {
    try {
      console.log(`[Coder Companion] Getting stored data for key: cc_${key}`);
      const data = localStorage.getItem(`cc_${key}`);
      console.log(`[Coder Companion] Raw data from localStorage:`, data);

      if (data) {
        const parsed = JSON.parse(data);
        console.log(`[Coder Companion] Parsed data for ${key}:`, parsed);

        // Ensure we return an array for list data
        if (Array.isArray(parsed)) {
          return parsed;
        } else if (parsed && typeof parsed === 'object') {
          // If it's an object but not an array, wrap it in an array
          return [parsed];
        } else {
          console.warn(`[Coder Companion] Data for ${key} is not an array or object:`, parsed);
          return defaultValue || [];
        }
      } else {
        console.log(`[Coder Companion] No data found for ${key}, returning default:`, defaultValue);
        return defaultValue || [];
      }
    } catch (error) {
      console.error(`[Coder Companion] Failed to get stored data for ${key}:`, error);
      return defaultValue || [];
    }
  }

  async setStoredData(key, value) {
    try {
      localStorage.setItem(`cc_${key}`, JSON.stringify(value));
      console.log(`[Coder Companion] Stored data for ${key}`);
      return true;
    } catch (error) {
      console.error(`[Coder Companion] Failed to store data for ${key}:`, error);
      return false;
    }
  }

  // Update getSampleData to use real data when available
  async getSampleData(tabName) {
    console.log(`[Coder Companion] Getting data for ${tabName}`);
    let realData = [];

    try {
      switch (tabName) {
        case 'projects':
          realData = await this.getStoredData('projects', []);
          console.log(`[Coder Companion] Real projects data:`, realData);
          break;
        case 'personas':
          realData = await this.getStoredData('personas', []);
          console.log(`[Coder Companion] Real personas data:`, realData);
          break;
        case 'artifacts':
          realData = await this.getStoredData('artifacts', []);
          console.log(`[Coder Companion] Real artifacts data:`, realData);
          break;
        case 'export':
          return [
            { id: '1', name: 'Current Project Export', status: 'ready', lastModified: 'Available now' },
            { id: '2', name: 'All Projects Archive', status: 'ready', lastModified: 'Available now' }
          ];
      }
    } catch (error) {
      console.error(`[Coder Companion] Failed to get real data for ${tabName}:`, error);
      realData = [];
    }

    // Ensure realData is an array
    if (!Array.isArray(realData)) {
      console.warn(`[Coder Companion] Real data is not an array for ${tabName}:`, realData);
      realData = [];
    }

    // If we have real data, return it
    if (realData && realData.length > 0) {
      console.log(`[Coder Companion] Returning real data for ${tabName}:`, realData.length, 'items');
      return realData;
    }

    // Otherwise return sample data
    console.log(`[Coder Companion] No real data, returning sample data for ${tabName}`);
    switch (tabName) {
      case 'projects':
        return [
          { id: '1', name: 'AI Assistant Development', status: 'active', lastModified: '2 hours ago' },
          { id: '2', name: 'Code Review Tool', status: 'draft', lastModified: '1 day ago' }
        ];
      case 'personas':
        return [
          { id: '1', name: 'Senior Frontend Developer', status: 'active', lastModified: '30 minutes ago' },
          { id: '2', name: 'Backend Architect', status: 'active', lastModified: '2 hours ago' }
        ];
      case 'artifacts':
        return [
          { id: '1', name: 'API Documentation', status: 'completed', lastModified: '1 hour ago' },
          { id: '2', name: 'Database Schema', status: 'draft', lastModified: '3 hours ago' }
        ];
      default:
        return [];
    }
  }

  // Export functionality
  async performExport(options) {
    try {
      const exportData = {};

      if (options.projects) {
        exportData.projects = await this.getStoredData('projects', []);
      }

      if (options.personas) {
        exportData.personas = await this.getStoredData('personas', []);
      }

      if (options.artifacts) {
        exportData.artifacts = await this.getStoredData('artifacts', []);
      }

      // Add metadata
      exportData.metadata = {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'Coder Companion Extension'
      };

      let content, filename, mimeType;

      switch (options.format) {
        case 'json':
          content = JSON.stringify(exportData, null, 2);
          filename = `coder-companion-export-${new Date().toISOString().split('T')[0]}.json`;
          mimeType = 'application/json';
          break;

        case 'markdown':
          content = this.convertToMarkdown(exportData);
          filename = `coder-companion-export-${new Date().toISOString().split('T')[0]}.md`;
          mimeType = 'text/markdown';
          break;

        case 'csv':
          content = this.convertToCSV(exportData);
          filename = `coder-companion-export-${new Date().toISOString().split('T')[0]}.csv`;
          mimeType = 'text/csv';
          break;

        default:
          throw new Error('Unsupported export format');
      }

      // Create and download file
      this.downloadFile(content, filename, mimeType);
      this.showNotification(`Data exported successfully as ${options.format.toUpperCase()}!`, 'success');

    } catch (error) {
      console.error('[Coder Companion] Export failed:', error);
      this.showNotification('Export failed. Please try again.', 'error');
    }
  }

  convertToMarkdown(data) {
    let markdown = '# Coder Companion Export\n\n';
    markdown += `Exported on: ${new Date().toLocaleDateString()}\n\n`;

    if (data.projects && data.projects.length > 0) {
      markdown += '## Projects\n\n';
      data.projects.forEach(project => {
        markdown += `### ${project.name}\n`;
        markdown += `- **Status**: ${project.status}\n`;
        markdown += `- **Priority**: ${project.priority}\n`;
        markdown += `- **Description**: ${project.description || 'No description'}\n`;
        markdown += `- **Tags**: ${project.tags.join(', ') || 'None'}\n`;
        markdown += `- **Created**: ${new Date(project.createdAt).toLocaleDateString()}\n\n`;
      });
    }

    if (data.personas && data.personas.length > 0) {
      markdown += '## Personas\n\n';
      data.personas.forEach(persona => {
        markdown += `### ${persona.name}\n`;
        markdown += `- **Role**: ${persona.role}\n`;
        markdown += `- **Personality**: ${persona.personality}\n`;
        markdown += `- **Description**: ${persona.description || 'No description'}\n`;
        markdown += `- **Expertise**: ${persona.expertise.join(', ') || 'None'}\n`;
        markdown += `- **System Prompt**: ${persona.systemPrompt || 'None'}\n\n`;
      });
    }

    if (data.artifacts && data.artifacts.length > 0) {
      markdown += '## Artifacts\n\n';
      data.artifacts.forEach(artifact => {
        markdown += `### ${artifact.name}\n`;
        markdown += `- **Type**: ${artifact.type}\n`;
        markdown += `- **Status**: ${artifact.status}\n`;
        markdown += `- **Word Count**: ${artifact.wordCount}\n`;
        markdown += `- **Tags**: ${artifact.tags.join(', ') || 'None'}\n`;
        markdown += `- **Content**:\n\`\`\`\n${artifact.content}\n\`\`\`\n\n`;
      });
    }

    return markdown;
  }

  convertToCSV(data) {
    let csv = '';

    if (data.projects && data.projects.length > 0) {
      csv += 'Type,Name,Status,Priority,Description,Tags,Created\n';
      data.projects.forEach(project => {
        csv += `Project,"${project.name}","${project.status}","${project.priority}","${project.description || ''}","${project.tags.join('; ')}","${project.createdAt}"\n`;
      });
      csv += '\n';
    }

    if (data.personas && data.personas.length > 0) {
      csv += 'Type,Name,Role,Personality,Description,Expertise,Created\n';
      data.personas.forEach(persona => {
        csv += `Persona,"${persona.name}","${persona.role}","${persona.personality}","${persona.description || ''}","${persona.expertise.join('; ')}","${persona.createdAt}"\n`;
      });
      csv += '\n';
    }

    if (data.artifacts && data.artifacts.length > 0) {
      csv += 'Type,Name,ArtifactType,Status,WordCount,Tags,Created\n';
      data.artifacts.forEach(artifact => {
        csv += `Artifact,"${artifact.name}","${artifact.type}","${artifact.status}","${artifact.wordCount}","${artifact.tags.join('; ')}","${artifact.createdAt}"\n`;
      });
    }

    return csv;
  }

  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
  }

  // Notification system
  showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.cc-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `cc-notification cc-notification-${type}`;
    notification.innerHTML = `
      <div class="cc-notification-content">
        <span class="cc-notification-message">${message}</span>
        <button class="cc-notification-close" onclick="this.parentElement.parentElement.remove()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    `;

    // Add notification styles if not already added
    this.addNotificationStyles();

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);

    return notification;
  }

  addNotificationStyles() {
    if (document.getElementById('cc-notification-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'cc-notification-styles';
    styles.textContent = `
      .cc-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10001;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-left: 4px solid;
        animation: slideInRight 0.3s ease-out;
        max-width: 400px;
      }

      .cc-notification-success {
        border-left-color: #10b981;
      }

      .cc-notification-error {
        border-left-color: #ef4444;
      }

      .cc-notification-info {
        border-left-color: #3b82f6;
      }

      .cc-notification-warning {
        border-left-color: #f59e0b;
      }

      .cc-notification-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        gap: 12px;
      }

      .cc-notification-message {
        font-size: 14px;
        color: #374151;
        line-height: 1.4;
      }

      .cc-notification-close {
        background: none;
        border: none;
        padding: 4px;
        cursor: pointer;
        color: #9ca3af;
        border-radius: 4px;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .cc-notification-close:hover {
        background: #f3f4f6;
        color: #6b7280;
      }

      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;

    document.head.appendChild(styles);
  }

  initializeSidebarComponents() {
    console.log('[Coder Companion] Initializing sidebar components...');

    // Wait for sidebar content to be fully loaded
    const checkSidebarReady = () => {
      const sidebarDocument = this.sidebarContainer.contentDocument;
      if (sidebarDocument && sidebarDocument.readyState === 'complete') {
        // Sidebar is ready, we can now interact with it
        this.setupSidebarCommunication();
      } else {
        // Check again in a moment
        setTimeout(checkSidebarReady, 50);
      }
    };

    checkSidebarReady();
  }

  setupMessageListener() {
    console.log('[Coder Companion] Setting up message listener...');

    // Listen for messages from the popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('[Coder Companion] Received message:', message);

      if (message.type === 'PING') {
        console.log('[Coder Companion] Received PING, responding with PONG');
        const response = { type: 'PONG', isReady: this.isInitialized && this.isInjected };
        console.log('[Coder Companion] PING response:', response);
        sendResponse(response);
        return true;
      }

      if (message.type === 'OPEN_SIDEBAR') {
        console.log('[Coder Companion] Received OPEN_SIDEBAR message');
        console.log('[Coder Companion] Extension state:', {
          isInitialized: this.isInitialized,
          isInjected: this.isInjected,
          sidebarContainer: !!this.sidebarContainer,
          isSidebarOpen: this.isSidebarOpen
        });

        // Ensure extension is fully initialized before opening sidebar
        if (!this.isInitialized || !this.isInjected) {
          console.warn('[Coder Companion] Extension not fully initialized, waiting...');
          // Wait for initialization to complete
          const checkReady = () => {
            if (this.isInitialized && this.isInjected) {
              try {
                this.openSidebar();
                console.log('[Coder Companion] Sidebar opened successfully');
                const response = { success: true };
                console.log('[Coder Companion] OPEN_SIDEBAR response:', response);
                sendResponse(response);
              } catch (error) {
                console.error('[Coder Companion] Failed to open sidebar:', error);
                const response = { success: false, error: error.message };
                console.log('[Coder Companion] OPEN_SIDEBAR error response:', response);
                sendResponse(response);
              }
            } else {
              setTimeout(checkReady, 100);
            }
          };
          checkReady();
        } else {
          try {
            this.openSidebar();
            console.log('[Coder Companion] Sidebar opened successfully');
            const response = { success: true };
            console.log('[Coder Companion] OPEN_SIDEBAR response:', response);
            sendResponse(response);
          } catch (error) {
            console.error('[Coder Companion] Failed to open sidebar:', error);
            const response = { success: false, error: error.message };
            console.log('[Coder Companion] OPEN_SIDEBAR error response:', response);
            sendResponse(response);
          }
        }
      }
      return true; // Keep the message channel open for async response
    });

    console.log('[Coder Companion] Message listener set up successfully');
  }

  setupSidebarCommunication() {
    console.log('[Coder Companion] Setting up sidebar communication...');

    // Listen for messages from the sidebar
    const sidebarWindow = this.sidebarContainer.contentWindow;

    if (sidebarWindow) {
      // Handle sidebar close events
      sidebarWindow.addEventListener('message', (event) => {
        if (event.data.type === 'SIDEBAR_CLOSE') {
          this.closeSidebar();
        }
      });

      // Send initialization message to sidebar
      sidebarWindow.postMessage({
        type: 'SIDEBAR_INIT',
        data: {
          url: window.location.href,
          timestamp: Date.now()
        }
      }, '*');
    }
  }

  saveExtensionState() {
    const state = {
      sidebarOpen: this.isSidebarOpen,
      lastActiveTab: 'projects' // Default tab
    };

    chrome.storage.local.set(state);
  }
}

// Initialize when script loads
console.log('[Coder Companion] Content script loaded');
new CoderCompanionInjector().init();

// Save state on page unload
window.addEventListener('beforeunload', () => {
  // Note: This is a placeholder - actual implementation will be in the injector instance
  console.log('[Coder Companion] Saving extension state...');
});