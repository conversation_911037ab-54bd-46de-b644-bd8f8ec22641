<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Extension Storage</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .test-section h3 {
      margin-top: 0;
      color: #333;
    }
    button {
      background: #007cba;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #005a87;
    }
    .results {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
      margin-top: 10px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .error {
      color: #dc3545;
    }
    .success {
      color: #28a745;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🧪 Test Extension Storage</h1>
    <p>Ce fichier teste si l'extension peut accéder au stockage Chrome correctement.</p>

    <div class="test-section">
      <h3>1. Test Chrome Storage Availability</h3>
      <button onclick="testChromeStorage()">Test Chrome Storage</button>
      <div id="chrome-storage-result" class="results"></div>
    </div>

    <div class="test-section">
      <h3>2. Test Storage Manager</h3>
      <button onclick="testStorageManager()">Test Storage Manager</button>
      <div id="storage-manager-result" class="results"></div>
    </div>

    <div class="test-section">
      <h3>3. Test Project Data</h3>
      <button onclick="testProjectData()">Load Projects</button>
      <button onclick="clearProjectData()">Clear Projects</button>
      <div id="project-data-result" class="results"></div>
    </div>

    <div class="test-section">
      <h3>4. Test Sidebar Integration</h3>
      <button onclick="testSidebarIntegration()">Test Sidebar</button>
      <div id="sidebar-integration-result" class="results"></div>
    </div>
  </div>

  <script>
    function log(elementId, message, type = 'info') {
      const element = document.getElementById(elementId);
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
      element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
    }

    function clear(elementId) {
      document.getElementById(elementId).innerHTML = '';
    }

    async function testChromeStorage() {
      clear('chrome-storage-result');
      log('chrome-storage-result', 'Testing Chrome Storage availability...');

      if (typeof chrome === 'undefined') {
        log('chrome-storage-result', 'ERROR: chrome object not available', 'error');
        return;
      }

      if (!chrome.storage) {
        log('chrome-storage-result', 'ERROR: chrome.storage not available', 'error');
        return;
      }

      if (!chrome.storage.local) {
        log('chrome-storage-result', 'ERROR: chrome.storage.local not available', 'error');
        return;
      }

      log('chrome-storage-result', 'SUCCESS: Chrome storage is available', 'success');

      // Test basic storage operations
      try {
        const testKey = 'test_key';
        const testValue = { message: 'Hello from test!', timestamp: Date.now() };

        // Set value
        await new Promise((resolve) => {
          chrome.storage.local.set({ [testKey]: testValue }, resolve);
        });
        log('chrome-storage-result', 'SUCCESS: Test data stored', 'success');

        // Get value
        const result = await new Promise((resolve) => {
          chrome.storage.local.get([testKey], resolve);
        });
        log('chrome-storage-result', `SUCCESS: Test data retrieved: ${JSON.stringify(result[testKey])}`, 'success');

        // Remove value
        await new Promise((resolve) => {
          chrome.storage.local.remove([testKey], resolve);
        });
        log('chrome-storage-result', 'SUCCESS: Test data removed', 'success');

      } catch (error) {
        log('chrome-storage-result', `ERROR: Storage test failed: ${error.message}`, 'error');
      }
    }

    async function testStorageManager() {
      clear('storage-manager-result');
      log('storage-manager-result', 'Testing Storage Manager...');

      // Mock the storage manager from sidebar script
      const storagePrefix = 'cc_';
      const isExtensionContext = typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local;

      if (!isExtensionContext) {
        log('storage-manager-result', 'ERROR: Not in extension context', 'error');
        return;
      }

      const storage = {
        async get(key, defaultValue = null) {
          return new Promise((resolve) => {
            chrome.storage.local.get([storagePrefix + key], (result) => {
              const data = result[storagePrefix + key];
              const value = data !== undefined ? data : defaultValue;
              resolve(value);
            });
          });
        },
        async set(key, value) {
          return new Promise((resolve) => {
            chrome.storage.local.set({ [storagePrefix + key]: value }, () => {
              resolve(true);
            });
          });
        }
      };

      try {
        // Test storage manager
        await storage.set('test', { message: 'Storage manager test' });
        log('storage-manager-result', 'SUCCESS: Storage manager set test', 'success');

        const result = await storage.get('test');
        log('storage-manager-result', `SUCCESS: Storage manager get test: ${JSON.stringify(result)}`, 'success');

      } catch (error) {
        log('storage-manager-result', `ERROR: Storage manager test failed: ${error.message}`, 'error');
      }
    }

    async function testProjectData() {
      clear('project-data-result');
      log('project-data-result', 'Testing Project Data...');

      const storagePrefix = 'cc_';
      const projectsKey = 'projects';

      try {
        const result = await new Promise((resolve) => {
          chrome.storage.local.get([storagePrefix + projectsKey], resolve);
        });

        const projects = result[storagePrefix + projectsKey] || [];
        log('project-data-result', `Found ${projects.length} projects:`, 'success');
        
        if (projects.length > 0) {
          projects.forEach((project, index) => {
            log('project-data-result', `${index + 1}. ${project.name} (${project.status}) - ${project.description}`);
          });
        } else {
          log('project-data-result', 'No projects found. This might be why the extension shows empty data.', 'error');
        }

      } catch (error) {
        log('project-data-result', `ERROR: Failed to load projects: ${error.message}`, 'error');
      }
    }

    async function clearProjectData() {
      clear('project-data-result');
      log('project-data-result', 'Clearing project data...');

      const storagePrefix = 'cc_';
      const projectsKey = 'projects';

      try {
        await new Promise((resolve) => {
          chrome.storage.local.remove([storagePrefix + projectsKey], resolve);
        });
        log('project-data-result', 'SUCCESS: Project data cleared', 'success');
      } catch (error) {
        log('project-data-result', `ERROR: Failed to clear projects: ${error.message}`, 'error');
      }
    }

    async function testSidebarIntegration() {
      clear('sidebar-integration-result');
      log('sidebar-integration-result', 'Testing Sidebar Integration...');

      // This would test if the sidebar can be loaded
      log('sidebar-integration-result', 'This test would require the full extension context.');
      log('sidebar-integration-result', 'Please test the extension directly in Chrome.');
    }
  </script>
</body>
</html>
