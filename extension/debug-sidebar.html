<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Debug Sidebar - Extension Context</title>
  <link rel="stylesheet" href="sidebar/style.css">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    .debug-container {
      max-width: 400px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .debug-header {
      background: #dc2626;
      color: white;
      padding: 15px;
      text-align: center;
    }
    .debug-content {
      height: 600px;
      overflow: hidden;
    }
    .debug-info {
      padding: 15px;
      background: #fef2f2;
      border-bottom: 1px solid #fecaca;
      font-size: 12px;
    }
    .debug-log {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 300px;
      max-height: 200px;
      background: #1f2937;
      color: #f9fafb;
      padding: 10px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 11px;
      overflow-y: auto;
      z-index: 10000;
    }
  </style>
</head>
<body>
  <div class="debug-container">
    <div class="debug-header">
      <h2>🐛 Debug Sidebar</h2>
      <p>Extension Context Simulation</p>
    </div>
    <div class="debug-info">
      <strong>Mode:</strong> Extension Context Simulation<br>
      <strong>Chrome Runtime:</strong> <span id="chrome-status">Checking...</span><br>
      <strong>Panels Loaded:</strong> <span id="panels-status">Loading...</span>
    </div>
    <div class="debug-content">
      <div id="sidebar-container" class="sidebar-container">
        <!-- Sidebar Header -->
        <header class="sidebar-header">
          <h2>Coder Companion</h2>
        </header>

        <!-- Tab Navigation -->
        <nav class="sidebar-tabs">
          <button class="tab-button active" data-tab="projects">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2-2z"/>
              <path d="M8 5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v0"/>
            </svg>
            Projects
          </button>
          <button class="tab-button" data-tab="personas">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            Personas
          </button>
          <button class="tab-button" data-tab="artifacts">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
            </svg>
            Artifacts
          </button>
          <button class="tab-button" data-tab="export">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            Export
          </button>
          <button class="tab-button" data-tab="artifact-editor">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
            Editor
          </button>
          <button class="tab-button" data-tab="version-control">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="2"/>
              <path d="M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14"/>
            </svg>
            Versions
          </button>
        </nav>

        <!-- Tab Content -->
        <main class="sidebar-content">
          <!-- Projects Panel -->
          <div id="projects-tab" class="tab-content active">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Personas Panel -->
          <div id="personas-tab" class="tab-content">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Artifacts Panel -->
          <div id="artifacts-tab" class="tab-content">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Export Panel -->
          <div id="export-tab" class="tab-content">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Artifact Editor Panel -->
          <div id="artifact-editor-tab" class="tab-content">
            <!-- Artifact editor content will be loaded dynamically -->
          </div>

          <!-- Version Control Panel -->
          <div id="version-control-tab" class="tab-content">
            <!-- Version control content will be loaded dynamically -->
          </div>
        </main>
      </div>
    </div>
  </div>

  <div id="debug-log" class="debug-log">
    <div><strong>Debug Log:</strong></div>
  </div>

  <script>
    // Mock chrome.runtime for extension context simulation
    window.chrome = {
      runtime: {
        getURL: (path) => {
          console.log(`[Debug] chrome.runtime.getURL called with: ${path}`);
          return `./${path}`;
        }
      }
    };

    // Debug logging
    const debugLog = document.getElementById('debug-log');
    const originalConsoleLog = console.log;
    console.log = function(...args) {
      originalConsoleLog.apply(console, args);
      const logEntry = document.createElement('div');
      logEntry.textContent = args.join(' ');
      debugLog.appendChild(logEntry);
      debugLog.scrollTop = debugLog.scrollHeight;
    };

    // Update status indicators
    document.getElementById('chrome-status').textContent = 'Simulated ✅';

    // Monitor panels loading
    let checkPanelsInterval = setInterval(() => {
      if (window.sidebarManager && window.sidebarManager.panelsLoaded) {
        document.getElementById('panels-status').textContent = 'Loaded ✅';
        clearInterval(checkPanelsInterval);
      }
    }, 100);

    console.log('[Debug] Extension context simulation started');
  </script>
  <script src="utils/storage.js"></script>
  <script src="sidebar/script.js"></script>
</body>
</html>
