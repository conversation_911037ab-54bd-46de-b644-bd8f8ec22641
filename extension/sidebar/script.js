// extension/sidebar/script.js - Enhanced Sidebar functionality with dynamic panels

// Data Managers
class ProjectManager {
  constructor(storage) {
    this.storage = storage;
    this.PROJECTS_KEY = 'projects';
  }

  generateId() {
    return 'proj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  async createProject(data) {
    try {
      const projects = await this.storage.get(this.PROJECTS_KEY, []);

      const project = {
        id: this.generateId(),
        name: data.name || 'New Project',
        description: data.description || '',
        slug: this.generateSlug(data.name || 'New Project'),
        personas: data.personas || [],
        artifacts: data.artifacts || [],
        status: data.status || 'active',
        priority: data.priority || 'medium',
        tags: data.tags || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        version: 1,
        metadata: data.metadata || {}
      };

      projects.push(project);
      await this.storage.set(this.PROJECTS_KEY, projects);

      console.log('[ProjectManager] Created project:', project.name);
      return project;
    } catch (error) {
      console.error('[ProjectManager] Failed to create project:', error);
      throw error;
    }
  }

  async getAllProjects() {
    try {
      return await this.storage.get(this.PROJECTS_KEY, []);
    } catch (error) {
      console.error('[ProjectManager] Failed to get projects:', error);
      return [];
    }
  }

  async updateProject(id, updates) {
    try {
      const projects = await this.storage.get(this.PROJECTS_KEY, []);
      const index = projects.findIndex(p => p.id === id);

      if (index === -1) {
        throw new Error('Project not found');
      }

      projects[index] = {
        ...projects[index],
        ...updates,
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };

      await this.storage.set(this.PROJECTS_KEY, projects);
      return projects[index];
    } catch (error) {
      console.error('[ProjectManager] Failed to update project:', error);
      throw error;
    }
  }

  async deleteProject(id) {
    try {
      const projects = await this.storage.get(this.PROJECTS_KEY, []);
      const filteredProjects = projects.filter(p => p.id !== id);

      await this.storage.set(this.PROJECTS_KEY, filteredProjects);
      console.log('[ProjectManager] Deleted project:', id);
      return true;
    } catch (error) {
      console.error('[ProjectManager] Failed to delete project:', error);
      return false;
    }
  }

  generateSlug(name) {
    return name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}

class PersonaManager {
  constructor(storage) {
    this.storage = storage;
    this.PERSONAS_KEY = 'personas';
    this.ACTIVE_PERSONA_KEY = 'activePersona';
  }

  generateId() {
    return 'persona_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  async createPersona(data) {
    try {
      const personas = await this.storage.get(this.PERSONAS_KEY, []);

      const persona = {
        id: this.generateId(),
        name: data.name || 'New Persona',
        description: data.description || '',
        slug: this.generateSlug(data.name || 'New Persona'),
        role: data.role || 'assistant',
        expertise: data.expertise || [],
        personality: data.personality || 'professional',
        systemPrompt: data.systemPrompt || '',
        temperature: data.temperature || 0.7,
        maxTokens: data.maxTokens || 1000,
        contextWindow: data.contextWindow || 4096,
        tags: data.tags || [],
        avatar: data.avatar || null,
        color: data.color || this.generateRandomColor(),
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastUsed: null,
        usageCount: 0,
        metadata: data.metadata || {}
      };

      personas.push(persona);
      await this.storage.set(this.PERSONAS_KEY, personas);

      console.log('[PersonaManager] Created persona:', persona.name);
      return persona;
    } catch (error) {
      console.error('[PersonaManager] Failed to create persona:', error);
      throw error;
    }
  }

  async getAllPersonas() {
    try {
      return await this.storage.get(this.PERSONAS_KEY, []);
    } catch (error) {
      console.error('[PersonaManager] Failed to get personas:', error);
      return [];
    }
  }

  async setActivePersona(id) {
    try {
      const personas = await this.storage.get(this.PERSONAS_KEY, []);
      const persona = personas.find(p => p.id === id);

      if (!persona) {
        throw new Error('Persona not found');
      }

      await this.storage.set(this.ACTIVE_PERSONA_KEY, persona);

      // Update usage count
      persona.usageCount = (persona.usageCount || 0) + 1;
      persona.lastUsed = new Date().toISOString();

      const index = personas.findIndex(p => p.id === id);
      personas[index] = persona;
      await this.storage.set(this.PERSONAS_KEY, personas);

      console.log('[PersonaManager] Set active persona:', persona.name);
      return persona;
    } catch (error) {
      console.error('[PersonaManager] Failed to set active persona:', error);
      throw error;
    }
  }

  async getActivePersona() {
    try {
      return await this.storage.get(this.ACTIVE_PERSONA_KEY, null);
    } catch (error) {
      console.error('[PersonaManager] Failed to get active persona:', error);
      return null;
    }
  }

  generateSlug(name) {
    return name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  generateRandomColor() {
    const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}

class ArtifactManager {
  constructor(storage) {
    this.storage = storage;
    this.ARTIFACTS_KEY = 'artifacts';
  }

  generateId() {
    return 'artifact_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  async createArtifact(data) {
    try {
      const artifacts = await this.storage.get(this.ARTIFACTS_KEY, []);

      const artifact = {
        id: this.generateId(),
        name: data.name || 'New Artifact',
        title: data.title || data.name || 'New Artifact',
        content: data.content || '',
        slug: this.generateSlug(data.name || 'New Artifact'),
        projectId: data.projectId || null,
        personaId: data.personaId || null,
        status: data.status || 'draft',
        priority: data.priority || 'medium',
        tags: data.tags || [],
        wordCount: this.countWords(data.content || ''),
        version: 1,
        versions: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        metadata: data.metadata || {}
      };

      artifacts.push(artifact);
      await this.storage.set(this.ARTIFACTS_KEY, artifacts);

      console.log('[ArtifactManager] Created artifact:', artifact.name);
      return artifact;
    } catch (error) {
      console.error('[ArtifactManager] Failed to create artifact:', error);
      throw error;
    }
  }

  async getAllArtifacts() {
    try {
      return await this.storage.get(this.ARTIFACTS_KEY, []);
    } catch (error) {
      console.error('[ArtifactManager] Failed to get artifacts:', error);
      return [];
    }
  }

  countWords(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  generateSlug(name) {
    return name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}

class SidebarManager {
  constructor() {
    this.currentTab = 'projects';
    this.panels = {};
    this.modules = {};
    this.panelsLoaded = false;
    this.init();
  }

  async init() {
    console.log('[Sidebar] Initializing sidebar...');

    await this.loadModules();
    await this.loadPanels();
    this.setupEventListeners();
    this.loadInitialData();
  }

  async loadModules() {
    console.log('[Sidebar] Loading modules...');

    try {
      // Always use fallback storage in extension context
      this.storage = this.createFallbackStorage();

      // Initialize data managers
      this.projectManager = new ProjectManager(this.storage);
      this.personaManager = new PersonaManager(this.storage);
      this.artifactManager = new ArtifactManager(this.storage);

      // Initialize sample data if needed (for testing)
      await this.initializeSampleDataIfNeeded();

      console.log('[Sidebar] Modules loaded successfully');
    } catch (error) {
      console.error('[Sidebar] Failed to load modules:', error);
      this.storage = this.createFallbackStorage();
      this.projectManager = new ProjectManager(this.storage);
      this.personaManager = new PersonaManager(this.storage);
      this.artifactManager = new ArtifactManager(this.storage);
    }
  }

  async initializeSampleDataIfNeeded() {
    try {
      // Check if we already have data
      const projects = await this.storage.get('projects', []);
      const personas = await this.storage.get('personas', []);
      const artifacts = await this.storage.get('artifacts', []);

      // If no data exists, create some sample data
      if (projects.length === 0 && personas.length === 0 && artifacts.length === 0) {
        console.log('[Sidebar] No data found, initializing sample data...');

        // Create sample projects
        const sampleProjects = [
          {
            id: 'proj_' + Date.now() + '_sample1',
            name: 'AI Assistant Development',
            title: 'AI Assistant Development',
            description: 'Building a comprehensive AI assistant for developers',
            slug: 'ai-assistant-development',
            status: 'active',
            priority: 'high',
            tags: ['ai', 'development', 'assistant'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastModified: '2 hours ago',
            artifactCount: 15
          },
          {
            id: 'proj_' + Date.now() + '_sample2',
            name: 'Code Review Tool',
            title: 'Code Review Tool',
            description: 'Automated code review and optimization suggestions',
            slug: 'code-review-tool',
            status: 'draft',
            priority: 'medium',
            tags: ['code-review', 'automation'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastModified: '1 day ago',
            artifactCount: 8
          }
        ];

        await this.storage.set('projects', sampleProjects);
        console.log('[Sidebar] Sample projects created');
      }
    } catch (error) {
      console.error('[Sidebar] Failed to initialize sample data:', error);
    }
  }

  createFallbackStorage() {
    const storagePrefix = 'cc_';
    const isExtensionContext = typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local;

    if (isExtensionContext) {
      console.log('[Storage] Using chrome.storage.local');
      return {
        async get(key, defaultValue = null) {
          try {
            console.log(`[Storage] Getting ${key} from chrome.storage`);
            return new Promise((resolve) => {
              chrome.storage.local.get([storagePrefix + key], (result) => {
                const data = result[storagePrefix + key];
                const value = data !== undefined ? data : defaultValue;
                console.log(`[Storage] Got ${key}:`, value);
                resolve(value);
              });
            });
          } catch (error) {
            console.error(`[Storage] Failed to get ${key}:`, error);
            return defaultValue;
          }
        },
        async set(key, value) {
          try {
            console.log(`[Storage] Setting ${key} in chrome.storage:`, value);
            return new Promise((resolve) => {
              chrome.storage.local.set({ [storagePrefix + key]: value }, () => {
                console.log(`[Storage] Set ${key} successfully`);
                resolve(true);
              });
            });
          } catch (error) {
            console.error(`[Storage] Failed to set ${key}:`, error);
            return false;
          }
        },
        async remove(key) {
          try {
            console.log(`[Storage] Removing ${key} from chrome.storage`);
            return new Promise((resolve) => {
              chrome.storage.local.remove([storagePrefix + key], () => {
                resolve(true);
              });
            });
          } catch (error) {
            console.error(`[Storage] Failed to remove ${key}:`, error);
            return false;
          }
        },
        async clear() {
          try {
            return new Promise((resolve) => {
              chrome.storage.local.get(null, (items) => {
                const ccKeys = Object.keys(items).filter(key => key.startsWith(storagePrefix));
                if (ccKeys.length > 0) {
                  chrome.storage.local.remove(ccKeys, () => {
                    console.log(`[Storage] Cleared ${ccKeys.length} keys from chrome.storage`);
                    resolve(true);
                  });
                } else {
                  console.log('[Storage] No keys to clear');
                  resolve(true);
                }
              });
            });
          } catch (error) {
            console.error('[Storage] Failed to clear:', error);
            return false;
          }
        }
      };
    } else {
      console.log('[Storage] Using localStorage fallback');
      return {
        async get(key, defaultValue = null) {
          try {
            console.log(`[Storage] Getting ${key} from localStorage`);
            const data = localStorage.getItem(storagePrefix + key);
            const result = data ? JSON.parse(data) : defaultValue;
            console.log(`[Storage] Got ${key}:`, result);
            return result;
          } catch (error) {
            console.error(`[Storage] Failed to get ${key}:`, error);
            return defaultValue;
          }
        },
        async set(key, value) {
          try {
            console.log(`[Storage] Setting ${key} in localStorage:`, value);
            localStorage.setItem(storagePrefix + key, JSON.stringify(value));
            console.log(`[Storage] Set ${key} successfully`);
            return true;
          } catch (error) {
            console.error(`[Storage] Failed to set ${key}:`, error);
            return false;
          }
        },
        async remove(key) {
          try {
            console.log(`[Storage] Removing ${key} from localStorage`);
            localStorage.removeItem(storagePrefix + key);
            return true;
          } catch (error) {
            console.error(`[Storage] Failed to remove ${key}:`, error);
            return false;
          }
        },
        async clear() {
          try {
            const keys = Object.keys(localStorage);
            const ccKeys = keys.filter(key => key.startsWith(storagePrefix));
            ccKeys.forEach(key => localStorage.removeItem(key));
            console.log(`[Storage] Cleared ${ccKeys.length} keys from localStorage`);
            return true;
          } catch (error) {
            console.error('[Storage] Failed to clear:', error);
            return false;
          }
        }
      };
    }
  }

  async loadPanels() {
    console.log('[Sidebar] Loading panel components...');

    // Wait a bit to ensure DOM is ready
    await new Promise(resolve => setTimeout(resolve, 100));

    // Instead of loading from files, create panels directly in JavaScript
    console.log('[Sidebar] Creating projects panel...');
    this.createProjectsPanel();

    console.log('[Sidebar] Creating personas panel...');
    this.createPersonasPanel();

    console.log('[Sidebar] Creating artifacts panel...');
    this.createArtifactsPanel();

    console.log('[Sidebar] Creating export panel...');
    this.createExportPanel();

    this.panelsLoaded = true;
    console.log('[Sidebar] All panels loaded, panelsLoaded =', this.panelsLoaded);
  }

  createProjectsPanel() {
    console.log('[Sidebar] Looking for projects-tab element...');
    const tabElement = document.getElementById('projects-tab');
    console.log('[Sidebar] projects-tab element:', tabElement);
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-project-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Project
            </button>
            <button id="refresh-projects-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="project-search" class="cc-input" placeholder="Search projects...">
          </div>
        </div>
        <div class="panel-content">
          <div id="projects-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading projects...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="projects-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="projects-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Completed:</span>
              <span class="stat-value" id="projects-completed">0</span>
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created projects panel');
    }
  }

  createPersonasPanel() {
    const tabElement = document.getElementById('personas-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-persona-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Persona
            </button>
            <button id="refresh-personas-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="persona-search" class="cc-input" placeholder="Search personas...">
          </div>
        </div>
        <div class="panel-content">
          <div class="active-persona-section">
            <div class="section-header">
              <h4>Active Persona</h4>
            </div>
            <div id="active-persona-display" class="active-persona-display">
              <div class="no-active-persona">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
                <p>No active persona selected</p>
              </div>
            </div>
          </div>
          <div class="section-header">
            <h4>All Personas</h4>
          </div>
          <div id="personas-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading personas...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="personas-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="personas-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Usage:</span>
              <span class="stat-value" id="personas-usage">0</span>
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created personas panel');
    }
  }

  createArtifactsPanel() {
    const tabElement = document.getElementById('artifacts-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-artifact-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Artifact
            </button>
            <button id="refresh-artifacts-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="artifact-search" class="cc-input" placeholder="Search artifacts...">
          </div>
        </div>
        <div class="panel-content">
          <div class="project-filter-section">
            <div class="section-header">
              <h4>Filter by Project</h4>
            </div>
            <div class="project-filter">
              <select id="project-filter" class="cc-input">
                <option value="">All Projects</option>
              </select>
            </div>
          </div>
          <div class="section-header">
            <h4>Artifacts</h4>
          </div>
          <div id="artifacts-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading artifacts...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="artifacts-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Words:</span>
              <span class="stat-value" id="artifacts-words">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Versions:</span>
              <span class="stat-value" id="artifacts-versions">0</span>
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created artifacts panel');
    }
  }

  createExportPanel() {
    const tabElement = document.getElementById('export-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="refresh-export-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="panel-content">
          <div class="export-actions-section">
            <div class="section-header">
              <h4>Quick Export</h4>
            </div>
            <div class="export-actions-grid">
              <button id="export-current-project-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Current Project
              </button>
              <button id="export-all-projects-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                All Projects
              </button>
              <button id="export-current-artifact-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                </svg>
                Current Artifact
              </button>
              <button id="export-templates-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                  <line x1="9" y1="12" x2="15" y2="12"/>
                </svg>
                Templates
              </button>
            </div>
          </div>
          <div id="export-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading export options...
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created export panel');
    }
  }

  createFallbackPanel(panelName) {
    const tabElement = document.getElementById(`${panelName}-tab`);
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <h3>${panelName.charAt(0).toUpperCase() + panelName.slice(1)}</h3>
        </div>
        <div class="panel-content">
          <div id="${panelName}-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading ${panelName}...
            </div>
          </div>
        </div>
      `;
    }
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        this.switchTab(tabId);
      });
    });

    // Sidebar close button
    document.getElementById('sidebar-close').addEventListener('click', () => {
      this.closeSidebar();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.closeSidebar();
      }
    });

    // Search functionality
    this.setupSearch();
  }

  switchTab(tabId) {
    console.log(`[Sidebar] Switching to tab: ${tabId}`);

    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabId}-tab`).classList.add('active');

    this.currentTab = tabId;

    // Load tab-specific data
    this.loadTabData(tabId);
  }

  async loadInitialData() {
    // Wait for panels to be loaded before loading data
    if (!this.panelsLoaded) {
      setTimeout(() => this.loadInitialData(), 100);
      return;
    }

    // Setup panel-specific event listeners
    this.setupPanelEventListeners();

    // Load data for the default tab
    await this.loadTabData(this.currentTab);
  }

  async loadTabData(tabId) {
    if (!this.panelsLoaded) {
      console.log('[Sidebar] Panels not loaded yet, waiting...');
      setTimeout(() => this.loadTabData(tabId), 100);
      return;
    }

    const listElement = document.getElementById(`${tabId}-list`);
    if (!listElement) {
      console.warn(`[Sidebar] List element not found for tab: ${tabId}`);
      return;
    }

    // Show loading state
    listElement.innerHTML = `
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading ${tabId}...
      </div>
    `;

    try {
      // Load real data from storage
      let data = [];

      switch (tabId) {
        case 'projects':
          data = await this.projectManager.getAllProjects();
          break;
        case 'personas':
          data = await this.personaManager.getAllPersonas();
          // Also update active persona display
          await this.updateActivePersonaDisplay();
          break;
        case 'artifacts':
          data = await this.artifactManager.getAllArtifacts();
          break;
        case 'export':
          data = this.getExportOptions();
          break;
        default:
          data = [];
      }

      // If no data exists, show some sample data for demo
      if (data.length === 0 && tabId !== 'export') {
        data = this.getMockData(tabId);
      }

      this.renderList(tabId, data);
      this.updateStats(tabId, data);

    } catch (error) {
      console.error(`[Sidebar] Failed to load ${tabId}:`, error);
      listElement.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
            <path d="M12 17h.01"/>
          </svg>
          <h3>Failed to load data</h3>
          <p>Please try again later</p>
        </div>
      `;
    }
  }

  async updateActivePersonaDisplay() {
    const activePersonaDisplay = document.getElementById('active-persona-display');
    if (!activePersonaDisplay) return;

    try {
      const activePersona = await this.personaManager.getActivePersona();

      if (activePersona) {
        activePersonaDisplay.innerHTML = `
          <div class="active-persona-card" style="border-left: 4px solid ${activePersona.color}">
            <div class="active-persona-info">
              <h5>${activePersona.name}</h5>
              <p>${activePersona.description}</p>
              <div class="active-persona-meta">
                <span class="persona-role">${activePersona.role}</span>
                <span class="persona-usage">Used ${activePersona.usageCount || 0} times</span>
              </div>
            </div>
            <button id="clear-active-persona-btn" class="cc-button secondary small">
              Clear
            </button>
          </div>
        `;

        // Add clear button listener
        const clearBtn = document.getElementById('clear-active-persona-btn');
        if (clearBtn) {
          clearBtn.addEventListener('click', async () => {
            await this.storage.remove('activePersona');
            this.updateActivePersonaDisplay();
          });
        }
      } else {
        activePersonaDisplay.innerHTML = `
          <div class="no-active-persona">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            <p>No active persona selected</p>
          </div>
        `;
      }
    } catch (error) {
      console.error('[Sidebar] Failed to update active persona display:', error);
    }
  }

  getExportOptions() {
    return [
      {
        id: '1',
        title: 'Current Project Export',
        description: 'Export the currently active project with all artifacts',
        status: 'ready',
        lastModified: 'Available now',
        type: 'project'
      },
      {
        id: '2',
        title: 'All Projects Archive',
        description: 'Complete backup of all projects and data',
        status: 'ready',
        lastModified: 'Available now',
        type: 'archive'
      },
      {
        id: '3',
        title: 'Personas Collection',
        description: 'Export all personas and their configurations',
        status: 'ready',
        lastModified: 'Available now',
        type: 'personas'
      },
      {
        id: '4',
        title: 'Templates Package',
        description: 'Export reusable templates and workflows',
        status: 'ready',
        lastModified: 'Available now',
        type: 'templates'
      }
    ];
  }

  getMockData(tabId) {
    switch (tabId) {
      case 'projects':
        return [
          {
            id: '1',
            title: 'AI Assistant Development',
            description: 'Building a comprehensive AI assistant for developers',
            status: 'active',
            lastModified: '2 hours ago',
            artifactCount: 15
          },
          {
            id: '2',
            title: 'Code Review Tool',
            description: 'Automated code review and optimization suggestions',
            status: 'draft',
            lastModified: '1 day ago',
            artifactCount: 8
          }
        ];

      case 'personas':
        return [
          {
            id: '1',
            title: 'Senior Frontend Developer',
            description: 'Expert in React, TypeScript, and modern web development',
            status: 'active',
            lastModified: '30 minutes ago'
          },
          {
            id: '2',
            title: 'Backend Architect',
            description: 'Specializes in scalable systems and API design',
            status: 'active',
            lastModified: '2 hours ago'
          }
        ];

      case 'artifacts':
        return [
          {
            id: '1',
            title: 'API Documentation',
            description: 'Comprehensive API documentation with examples',
            status: 'completed',
            lastModified: '1 hour ago',
            wordCount: 1250,
            versions: 3
          },
          {
            id: '2',
            title: 'Database Schema',
            description: 'Database design and migration scripts',
            status: 'draft',
            lastModified: '3 hours ago',
            wordCount: 450,
            versions: 1
          }
        ];

      case 'export':
        return [
          {
            id: '1',
            title: 'Current Project Export',
            description: 'Export the currently active project with all artifacts',
            status: 'ready',
            lastModified: 'Available now',
            type: 'project'
          },
          {
            id: '2',
            title: 'All Projects Archive',
            description: 'Complete backup of all projects and data',
            status: 'ready',
            lastModified: 'Available now',
            type: 'archive'
          },
          {
            id: '3',
            title: 'Personas Collection',
            description: 'Export all personas and their configurations',
            status: 'ready',
            lastModified: 'Available now',
            type: 'personas'
          },
          {
            id: '4',
            title: 'Templates Package',
            description: 'Export reusable templates and workflows',
            status: 'ready',
            lastModified: 'Available now',
            type: 'templates'
          }
        ];

      default:
        return [];
    }
  }

  renderList(tabId, items) {
    const listElement = document.getElementById(`${tabId}-list`);

    if (items.length === 0) {
      listElement.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5.586a1 1 0 0 1 .707.293l5.414 5.414a1 1 0 0 1 .293.707V19a2 2 0 0 1-2 2z"/>
          </svg>
          <h3>No ${tabId} found</h3>
          <p>Create your first ${tabId.slice(0, -1)} to get started</p>
        </div>
      `;
      return;
    }

    const itemsHtml = items.map(item => {
      const itemClass = `${tabId.slice(0, -1)}-item`;
      const titleClass = `${tabId.slice(0, -1)}-title`;
      const metaClass = `${tabId.slice(0, -1)}-meta`;
      const descClass = `${tabId.slice(0, -1)}-description`;

      return `
        <div class="cc-list-item ${itemClass}" data-id="${item.id}">
          <div class="${titleClass}">${item.title}</div>
          <div class="${metaClass}">
            <span class="status-indicator ${item.status}">${item.status}</span>
            <span>Last modified: ${item.lastModified}</span>
            ${item.artifactCount ? `<span>${item.artifactCount} artifacts</span>` : ''}
            ${item.wordCount ? `<span>${item.wordCount} words</span>` : ''}
          </div>
          ${item.description ? `<div class="${descClass}">${item.description}</div>` : ''}
        </div>
      `;
    }).join('');

    listElement.innerHTML = itemsHtml;

    // Add click handlers
    listElement.querySelectorAll('.cc-list-item').forEach(item => {
      item.addEventListener('click', () => {
        this.handleItemClick(tabId, item.dataset.id);
      });
    });
  }

  handleItemClick(tabId, itemId) {
    console.log(`[Sidebar] Clicked ${tabId} item: ${itemId}`);

    // Get the item data
    const mockData = this.getMockData(tabId);
    const item = mockData.find(i => i.id === itemId);

    if (!item) {
      console.warn(`[Sidebar] Item not found: ${itemId}`);
      return;
    }

    // Handle different tab actions
    switch (tabId) {
      case 'projects':
        this.handleProjectClick(item);
        break;
      case 'personas':
        this.handlePersonaClick(item);
        break;
      case 'artifacts':
        this.handleArtifactClick(item);
        break;
      case 'export':
        this.handleExportClick(item);
        break;
      default:
        console.log(`[Sidebar] No handler for tab: ${tabId}`);
    }
  }

  handleProjectClick(project) {
    console.log(`[Sidebar] Opening project: ${project.title || project.name}`);
    this.showProjectDetails(project);
  }

  async handlePersonaClick(persona) {
    console.log(`[Sidebar] Opening persona details: ${persona.title || persona.name}`);
    this.showPersonaDetails(persona);
  }

  showPersonaDetails(persona) {
    const modal = this.createModal(`Persona: ${persona.name}`, `
      <div class="persona-details">
        <div class="persona-info">
          <div class="info-group">
            <label>Name:</label>
            <span>${persona.name}</span>
          </div>
          <div class="info-group">
            <label>Role:</label>
            <span>${persona.role || 'Assistant'}</span>
          </div>
          <div class="info-group">
            <label>Personality:</label>
            <span>${persona.personality || 'Professional'}</span>
          </div>
          ${persona.description ? `
            <div class="info-group">
              <label>Description:</label>
              <span>${persona.description}</span>
            </div>
          ` : ''}
          ${persona.expertise && persona.expertise.length > 0 ? `
            <div class="info-group">
              <label>Expertise:</label>
              <div class="tags">
                ${persona.expertise.map(skill => `<span class="tag">${skill}</span>`).join('')}
              </div>
            </div>
          ` : ''}
          ${persona.systemPrompt ? `
            <div class="info-group">
              <label>System Prompt:</label>
              <div class="system-prompt">${persona.systemPrompt}</div>
            </div>
          ` : ''}
          <div class="info-group">
            <label>Created:</label>
            <span>${new Date(persona.createdAt).toLocaleDateString()}</span>
          </div>
          <div class="info-group">
            <label>Last Used:</label>
            <span>${persona.lastUsed ? new Date(persona.lastUsed).toLocaleDateString() : 'Never'}</span>
          </div>
          <div class="info-group">
            <label>Usage Count:</label>
            <span>${persona.usageCount || 0}</span>
          </div>
        </div>
        <div class="persona-actions">
          <button class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Close</button>
          <button class="cc-button primary" onclick="window.sidebarApp.activatePersona('${persona.id}'); this.closest('.modal-overlay').remove();">Activate</button>
          <button class="cc-button primary" onclick="window.sidebarApp.showEditPersonaModal('${persona.id}'); this.closest('.modal-overlay').remove();">Edit Persona</button>
        </div>
      </div>
    `);
  }

  async activatePersona(personaId) {
    try {
      await this.personaManager.setActivePersona(personaId);
      await this.updateActivePersonaDisplay();
      const persona = await this.personaManager.getPersona(personaId);
      this.showNotification(`Activated persona: ${persona.name}`, 'success');
    } catch (error) {
      console.error('[Sidebar] Failed to activate persona:', error);
      this.showNotification('Failed to activate persona', 'error');
    }
  }

  showProjectDetails(project) {
    const modal = this.createModal(`Project: ${project.name}`, `
      <div class="project-details">
        <div class="project-info">
          <div class="info-group">
            <label>Name:</label>
            <span>${project.name}</span>
          </div>
          <div class="info-group">
            <label>Description:</label>
            <span>${project.description || 'No description'}</span>
          </div>
          <div class="info-group">
            <label>Status:</label>
            <span class="status-indicator ${project.status}">${project.status}</span>
          </div>
          <div class="info-group">
            <label>Priority:</label>
            <span class="priority-indicator ${project.priority}">${project.priority}</span>
          </div>
          <div class="info-group">
            <label>Created:</label>
            <span>${new Date(project.createdAt).toLocaleDateString()}</span>
          </div>
          <div class="info-group">
            <label>Last Modified:</label>
            <span>${new Date(project.lastModified).toLocaleDateString()}</span>
          </div>
          ${project.tags && project.tags.length > 0 ? `
            <div class="info-group">
              <label>Tags:</label>
              <div class="tags">
                ${project.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
              </div>
            </div>
          ` : ''}
        </div>
        <div class="project-actions">
          <button class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Close</button>
          <button class="cc-button primary" onclick="window.sidebarApp.showEditProjectModal('${project.id}'); this.closest('.modal-overlay').remove();">Edit Project</button>
        </div>
      </div>
    `);
  }

  handleArtifactClick(artifact) {
    console.log(`[Sidebar] Opening artifact details: ${artifact.title || artifact.name}`);
    this.showArtifactDetails(artifact);
  }

  showArtifactDetails(artifact) {
    const modal = this.createModal(`Artifact: ${artifact.name}`, `
      <div class="artifact-details">
        <div class="artifact-info">
          <div class="info-group">
            <label>Name:</label>
            <span>${artifact.name}</span>
          </div>
          <div class="info-group">
            <label>Type:</label>
            <span>${artifact.type || 'Document'}</span>
          </div>
          ${artifact.description ? `
            <div class="info-group">
              <label>Description:</label>
              <span>${artifact.description}</span>
            </div>
          ` : ''}
          <div class="info-group">
            <label>Word Count:</label>
            <span>${artifact.wordCount || 0} words</span>
          </div>
          <div class="info-group">
            <label>Version:</label>
            <span>${artifact.version || 1}</span>
          </div>
          <div class="info-group">
            <label>Created:</label>
            <span>${new Date(artifact.createdAt).toLocaleDateString()}</span>
          </div>
          <div class="info-group">
            <label>Last Modified:</label>
            <span>${new Date(artifact.lastModified).toLocaleDateString()}</span>
          </div>
          ${artifact.tags && artifact.tags.length > 0 ? `
            <div class="info-group">
              <label>Tags:</label>
              <div class="tags">
                ${artifact.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
              </div>
            </div>
          ` : ''}
          ${artifact.content ? `
            <div class="info-group">
              <label>Content Preview:</label>
              <div class="content-preview">${artifact.content.substring(0, 200)}${artifact.content.length > 200 ? '...' : ''}</div>
            </div>
          ` : ''}
        </div>
        <div class="artifact-actions">
          <button class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Close</button>
          <button class="cc-button primary" onclick="window.sidebarApp.showEditArtifactModal('${artifact.id}'); this.closest('.modal-overlay').remove();">Edit Artifact</button>
        </div>
      </div>
    `);
  }

  handleExportClick(exportItem) {
    console.log(`[Sidebar] Starting export: ${exportItem.title}`);
    // TODO: Implement export functionality
    alert(`Starting export: ${exportItem.title}\n\nThis functionality will be implemented soon.`);
  }

  setupSearch() {
    const searchInputs = ['project-search', 'persona-search', 'artifact-search'];

    searchInputs.forEach(inputId => {
      const input = document.getElementById(inputId);
      if (input) {
        input.addEventListener('input', (event) => {
          const query = event.target.value.toLowerCase();
          this.handleSearch(inputId.replace('-search', 's'), query);
        });
      }
    });
  }

  handleSearch(tabId, query) {
    const items = document.querySelectorAll(`#${tabId}-list .cc-list-item`);

    items.forEach(item => {
      const title = item.querySelector(`.${tabId.slice(0, -1)}-title`)?.textContent || '';
      const description = item.querySelector(`.${tabId.slice(0, -1)}-description`)?.textContent || '';

      const matches = title.toLowerCase().includes(query) ||
                     description.toLowerCase().includes(query);

      item.style.display = matches ? 'block' : 'none';
    });
  }

  closeSidebar() {
    // Send message to content script to close sidebar
    chrome.runtime.sendMessage({ type: 'CLOSE_SIDEBAR' }, (response) => {
      if (response?.success) {
        console.log('[Sidebar] Sidebar closed');
      }
    });

    // Also send message to parent window (content script)
    window.parent.postMessage({ type: 'SIDEBAR_CLOSE' }, '*');
  }

  // Public methods for external communication
  openTab(tabId) {
    if (tabId !== this.currentTab) {
      this.switchTab(tabId);
    }
  }

  refreshData() {
    this.loadTabData(this.currentTab);
  }

  handleEditFromContentScript(itemType, itemId, itemData = null) {
    console.log(`[Sidebar] Handling edit request from content script: ${itemType} ${itemId}`, itemData);

    try {
      // Switch to appropriate tab first
      let tabId;
      switch (itemType) {
        case 'project':
          tabId = 'projects';
          break;
        case 'persona':
          tabId = 'personas';
          break;
        case 'artifact':
          tabId = 'artifacts';
          break;
        default:
          console.error('[Sidebar] Unknown item type:', itemType);
          return;
      }

      // Switch to the tab
      this.switchTab(tabId);

      // Show notification that we're opening the editor
      this.showNotification(`Opening ${itemType} editor...`, 'info');

      // Wait a bit for the tab to load, then show edit modal
      setTimeout(() => {
        try {
          switch (itemType) {
            case 'project':
              this.showEditProjectModal(itemId);
              break;
            case 'persona':
              this.showEditPersonaModal(itemId);
              break;
            case 'artifact':
              this.showEditArtifactModal(itemId);
              break;
          }
          console.log(`[Sidebar] ${itemType} edit modal should be open now`);
        } catch (modalError) {
          console.error(`[Sidebar] Failed to open ${itemType} edit modal:`, modalError);
          this.showNotification(`Failed to open ${itemType} editor. Please try again.`, 'error');
        }
      }, 500); // Increased timeout for better reliability

    } catch (error) {
      console.error('[Sidebar] Failed to handle edit from content script:', error);
      this.showNotification(`Failed to open ${itemType} editor`, 'error');
    }
  }

  // Modal and Notification utilities
  createModal(title, content) {
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    modalOverlay.innerHTML = `
      <div class="modal">
        <div class="modal-header">
          <h3>${title}</h3>
          <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="modal-content">
          ${content}
        </div>
      </div>
    `;

    document.body.appendChild(modalOverlay);

    // Close on overlay click
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        modalOverlay.remove();
      }
    });

    // Close on Escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        modalOverlay.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    return modalOverlay;
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    `;

    // Add to page
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
      notificationContainer = document.createElement('div');
      notificationContainer.id = 'notification-container';
      notificationContainer.className = 'notification-container';
      document.body.appendChild(notificationContainer);
    }

    notificationContainer.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);

    return notification;
  }

  updateStats(tabId, data) {
    // Update statistics in the panel footer
    const totalElement = document.getElementById(`${tabId}-total`);
    if (totalElement) {
      totalElement.textContent = data.length;
    }

    switch (tabId) {
      case 'projects':
        const activeProjects = data.filter(p => p.status === 'active').length;
        const completedProjects = data.filter(p => p.status === 'completed').length;

        const activeElement = document.getElementById('projects-active');
        const completedElement = document.getElementById('projects-completed');

        if (activeElement) activeElement.textContent = activeProjects;
        if (completedElement) completedElement.textContent = completedProjects;
        break;

      case 'personas':
        const activePersonas = data.filter(p => p.status === 'active').length;
        const usageCount = data.reduce((sum, p) => sum + (p.usageCount || 0), 0);

        const personasActiveElement = document.getElementById('personas-active');
        const personasUsageElement = document.getElementById('personas-usage');

        if (personasActiveElement) personasActiveElement.textContent = activePersonas;
        if (personasUsageElement) personasUsageElement.textContent = usageCount;
        break;

      case 'artifacts':
        const totalWords = data.reduce((sum, a) => sum + (a.wordCount || 0), 0);
        const totalVersions = data.reduce((sum, a) => sum + (a.versions || 1), 0);

        const wordsElement = document.getElementById('artifacts-words');
        const versionsElement = document.getElementById('artifacts-versions');

        if (wordsElement) wordsElement.textContent = totalWords;
        if (versionsElement) versionsElement.textContent = totalVersions;
        break;
    }
  }

  setupPanelEventListeners() {
    // Setup event listeners for panel-specific buttons
    this.setupProjectsEventListeners();
    this.setupPersonasEventListeners();
    this.setupArtifactsEventListeners();
    this.setupExportEventListeners();
  }

  setupProjectsEventListeners() {
    const newProjectBtn = document.getElementById('new-project-btn');
    const refreshProjectsBtn = document.getElementById('refresh-projects-btn');

    if (newProjectBtn) {
      newProjectBtn.addEventListener('click', () => {
        console.log('[Sidebar] New project button clicked');
        this.showCreateProjectModal();
      });
    }

    if (refreshProjectsBtn) {
      refreshProjectsBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh projects button clicked');
        this.loadTabData('projects');
      });
    }
  }

  showCreateProjectModal() {
    const modal = this.createModal('Create New Project', `
      <form id="create-project-form" class="modal-form">
        <div class="form-group">
          <label for="project-name">Project Name *</label>
          <input type="text" id="project-name" class="cc-input" required placeholder="Enter project name">
        </div>
        <div class="form-group">
          <label for="project-description">Description</label>
          <textarea id="project-description" class="cc-input" rows="3" placeholder="Describe your project..."></textarea>
        </div>
        <div class="form-group">
          <label for="project-priority">Priority</label>
          <select id="project-priority" class="cc-input">
            <option value="low">Low</option>
            <option value="medium" selected>Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
        <div class="form-group">
          <label for="project-tags">Tags (comma-separated)</label>
          <input type="text" id="project-tags" class="cc-input" placeholder="web, frontend, react">
        </div>
        <div class="form-actions">
          <button type="button" class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
          <button type="submit" class="cc-button primary">Create Project</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-project-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleCreateProject(form, modal);
    });
  }

  async showEditProjectModal(projectId) {
    try {
      // Get the project data
      const project = await this.projectManager.getProject(projectId);
      if (!project) {
        this.showNotification('Project not found', 'error');
        return;
      }

      const modal = this.createModal('Edit Project', `
        <form id="edit-project-form" class="modal-form">
          <div class="form-group">
            <label for="edit-project-name">Project Name *</label>
            <input type="text" id="edit-project-name" class="cc-input" required placeholder="Enter project name" value="${project.name}">
          </div>
          <div class="form-group">
            <label for="edit-project-description">Description</label>
            <textarea id="edit-project-description" class="cc-input" rows="3" placeholder="Describe your project...">${project.description || ''}</textarea>
          </div>
          <div class="form-group">
            <label for="edit-project-status">Status</label>
            <select id="edit-project-status" class="cc-input">
              <option value="active" ${project.status === 'active' ? 'selected' : ''}>Active</option>
              <option value="draft" ${project.status === 'draft' ? 'selected' : ''}>Draft</option>
              <option value="completed" ${project.status === 'completed' ? 'selected' : ''}>Completed</option>
              <option value="archived" ${project.status === 'archived' ? 'selected' : ''}>Archived</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-project-priority">Priority</label>
            <select id="edit-project-priority" class="cc-input">
              <option value="low" ${project.priority === 'low' ? 'selected' : ''}>Low</option>
              <option value="medium" ${project.priority === 'medium' ? 'selected' : ''}>Medium</option>
              <option value="high" ${project.priority === 'high' ? 'selected' : ''}>High</option>
              <option value="urgent" ${project.priority === 'urgent' ? 'selected' : ''}>Urgent</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-project-tags">Tags (comma-separated)</label>
            <input type="text" id="edit-project-tags" class="cc-input" placeholder="web, frontend, react" value="${(project.tags || []).join(', ')}">
          </div>
          <div class="form-actions">
            <button type="button" class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
            <button type="submit" class="cc-button primary">Update Project</button>
          </div>
        </form>
      `);

      // Add form submit handler
      const form = modal.querySelector('#edit-project-form');
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await this.handleEditProject(projectId, form, modal);
      });

    } catch (error) {
      console.error('[Sidebar] Failed to show edit project modal:', error);
      this.showNotification('Failed to load project for editing', 'error');
    }
  }

  async handleCreateProject(form, modal) {
    try {
      const formData = new FormData(form);
      const tags = form.querySelector('#project-tags').value
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const projectData = {
        name: form.querySelector('#project-name').value,
        description: form.querySelector('#project-description').value,
        priority: form.querySelector('#project-priority').value,
        tags: tags
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create project
      const project = await this.projectManager.createProject(projectData);

      // Close modal
      modal.remove();

      // Refresh projects list
      await this.loadTabData('projects');

      // Show success message
      this.showNotification('Project created successfully!', 'success');

    } catch (error) {
      console.error('[Sidebar] Failed to create project:', error);
      this.showNotification('Failed to create project. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Project';
      submitBtn.disabled = false;
    }
  }

  async handleEditProject(projectId, form, modal) {
    try {
      const tags = form.querySelector('#edit-project-tags').value
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const updates = {
        name: form.querySelector('#edit-project-name').value,
        description: form.querySelector('#edit-project-description').value,
        status: form.querySelector('#edit-project-status').value,
        priority: form.querySelector('#edit-project-priority').value,
        tags: tags
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Updating...';
      submitBtn.disabled = true;

      // Update project
      const updatedProject = await this.projectManager.updateProject(projectId, updates);

      // Close modal
      modal.remove();

      // Refresh projects list
      await this.loadTabData('projects');

      // Show success message
      this.showNotification(`Project "${updatedProject.name}" updated successfully!`, 'success');

    } catch (error) {
      console.error('[Sidebar] Failed to update project:', error);
      this.showNotification('Failed to update project. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Update Project';
      submitBtn.disabled = false;
    }
  }

  setupPersonasEventListeners() {
    const newPersonaBtn = document.getElementById('new-persona-btn');
    const refreshPersonasBtn = document.getElementById('refresh-personas-btn');

    if (newPersonaBtn) {
      newPersonaBtn.addEventListener('click', () => {
        console.log('[Sidebar] New persona button clicked');
        this.showCreatePersonaModal();
      });
    }

    if (refreshPersonasBtn) {
      refreshPersonasBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh personas button clicked');
        this.loadTabData('personas');
      });
    }
  }

  showCreatePersonaModal() {
    const modal = this.createModal('Create New Persona', `
      <form id="create-persona-form" class="modal-form">
        <div class="form-group">
          <label for="persona-name">Persona Name *</label>
          <input type="text" id="persona-name" class="cc-input" required placeholder="e.g., Senior Frontend Developer">
        </div>
        <div class="form-group">
          <label for="persona-description">Description</label>
          <textarea id="persona-description" class="cc-input" rows="2" placeholder="Brief description of this persona..."></textarea>
        </div>
        <div class="form-group">
          <label for="persona-role">Role</label>
          <select id="persona-role" class="cc-input">
            <option value="assistant">Assistant</option>
            <option value="expert">Expert</option>
            <option value="reviewer">Reviewer</option>
            <option value="mentor">Mentor</option>
            <option value="analyst">Analyst</option>
          </select>
        </div>
        <div class="form-group">
          <label for="persona-personality">Personality</label>
          <select id="persona-personality" class="cc-input">
            <option value="professional">Professional</option>
            <option value="friendly">Friendly</option>
            <option value="technical">Technical</option>
            <option value="creative">Creative</option>
            <option value="concise">Concise</option>
          </select>
        </div>
        <div class="form-group">
          <label for="persona-expertise">Expertise (comma-separated)</label>
          <input type="text" id="persona-expertise" class="cc-input" placeholder="React, TypeScript, Node.js">
        </div>
        <div class="form-group">
          <label for="persona-system-prompt">System Prompt</label>
          <textarea id="persona-system-prompt" class="cc-input" rows="3" placeholder="You are a helpful assistant specialized in..."></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
          <button type="submit" class="cc-button primary">Create Persona</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-persona-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleCreatePersona(form, modal);
    });
  }

  async handleCreatePersona(form, modal) {
    try {
      const expertise = form.querySelector('#persona-expertise').value
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill.length > 0);

      const personaData = {
        name: form.querySelector('#persona-name').value,
        description: form.querySelector('#persona-description').value,
        role: form.querySelector('#persona-role').value,
        personality: form.querySelector('#persona-personality').value,
        expertise: expertise,
        systemPrompt: form.querySelector('#persona-system-prompt').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create persona
      const persona = await this.personaManager.createPersona(personaData);

      // Close modal
      modal.remove();

      // Refresh personas list
      await this.loadTabData('personas');

      // Show success message
      this.showNotification('Persona created successfully!', 'success');

    } catch (error) {
      console.error('[Sidebar] Failed to create persona:', error);
      this.showNotification('Failed to create persona. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Persona';
      submitBtn.disabled = false;
    }
  }

  async showEditPersonaModal(personaId) {
    try {
      // Get the persona data
      const persona = await this.personaManager.getPersona(personaId);
      if (!persona) {
        this.showNotification('Persona not found', 'error');
        return;
      }

      const modal = this.createModal('Edit Persona', `
        <form id="edit-persona-form" class="modal-form">
          <div class="form-group">
            <label for="edit-persona-name">Persona Name *</label>
            <input type="text" id="edit-persona-name" class="cc-input" required placeholder="e.g., Senior Frontend Developer" value="${persona.name}">
          </div>
          <div class="form-group">
            <label for="edit-persona-description">Description</label>
            <textarea id="edit-persona-description" class="cc-input" rows="3" placeholder="Describe this persona's background and purpose...">${persona.description || ''}</textarea>
          </div>
          <div class="form-group">
            <label for="edit-persona-role">Role</label>
            <select id="edit-persona-role" class="cc-input">
              <option value="assistant" ${persona.role === 'assistant' ? 'selected' : ''}>Assistant</option>
              <option value="developer" ${persona.role === 'developer' ? 'selected' : ''}>Developer</option>
              <option value="designer" ${persona.role === 'designer' ? 'selected' : ''}>Designer</option>
              <option value="analyst" ${persona.role === 'analyst' ? 'selected' : ''}>Analyst</option>
              <option value="consultant" ${persona.role === 'consultant' ? 'selected' : ''}>Consultant</option>
              <option value="other" ${persona.role === 'other' ? 'selected' : ''}>Other</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-persona-personality">Personality</label>
            <select id="edit-persona-personality" class="cc-input">
              <option value="professional" ${persona.personality === 'professional' ? 'selected' : ''}>Professional</option>
              <option value="friendly" ${persona.personality === 'friendly' ? 'selected' : ''}>Friendly</option>
              <option value="casual" ${persona.personality === 'casual' ? 'selected' : ''}>Casual</option>
              <option value="formal" ${persona.personality === 'formal' ? 'selected' : ''}>Formal</option>
              <option value="creative" ${persona.personality === 'creative' ? 'selected' : ''}>Creative</option>
              <option value="analytical" ${persona.personality === 'analytical' ? 'selected' : ''}>Analytical</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-persona-expertise">Expertise (comma-separated)</label>
            <input type="text" id="edit-persona-expertise" class="cc-input" placeholder="JavaScript, React, Node.js" value="${(persona.expertise || []).join(', ')}">
          </div>
          <div class="form-group">
            <label for="edit-persona-system-prompt">System Prompt</label>
            <textarea id="edit-persona-system-prompt" class="cc-input" rows="3" placeholder="You are a helpful assistant specialized in...">${persona.systemPrompt || ''}</textarea>
          </div>
          <div class="form-actions">
            <button type="button" class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
            <button type="submit" class="cc-button primary">Update Persona</button>
          </div>
        </form>
      `);

      // Add form submit handler
      const form = modal.querySelector('#edit-persona-form');
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await this.handleEditPersona(personaId, form, modal);
      });

    } catch (error) {
      console.error('[Sidebar] Failed to show edit persona modal:', error);
      this.showNotification('Failed to load persona for editing', 'error');
    }
  }

  async handleEditPersona(personaId, form, modal) {
    try {
      const expertise = form.querySelector('#edit-persona-expertise').value
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill.length > 0);

      const updates = {
        name: form.querySelector('#edit-persona-name').value,
        description: form.querySelector('#edit-persona-description').value,
        role: form.querySelector('#edit-persona-role').value,
        personality: form.querySelector('#edit-persona-personality').value,
        expertise: expertise,
        systemPrompt: form.querySelector('#edit-persona-system-prompt').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Updating...';
      submitBtn.disabled = true;

      // Update persona
      const updatedPersona = await this.personaManager.updatePersona(personaId, updates);

      // Close modal
      modal.remove();

      // Refresh personas list
      await this.loadTabData('personas');

      // Show success message
      this.showNotification(`Persona "${updatedPersona.name}" updated successfully!`, 'success');

    } catch (error) {
      console.error('[Sidebar] Failed to update persona:', error);
      this.showNotification('Failed to update persona. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Update Persona';
      submitBtn.disabled = false;
    }
  }

  async showEditArtifactModal(artifactId) {
    try {
      // Get the artifact data
      const artifact = await this.artifactManager.getArtifact(artifactId);
      if (!artifact) {
        this.showNotification('Artifact not found', 'error');
        return;
      }

      const modal = this.createModal('Edit Artifact', `
        <form id="edit-artifact-form" class="modal-form">
          <div class="form-group">
            <label for="edit-artifact-name">Artifact Name *</label>
            <input type="text" id="edit-artifact-name" class="cc-input" required placeholder="Enter artifact name" value="${artifact.name}">
          </div>
          <div class="form-group">
            <label for="edit-artifact-description">Description</label>
            <textarea id="edit-artifact-description" class="cc-input" rows="3" placeholder="Describe this artifact...">${artifact.description || ''}</textarea>
          </div>
          <div class="form-group">
            <label for="edit-artifact-type">Type</label>
            <select id="edit-artifact-type" class="cc-input">
              <option value="document" ${artifact.type === 'document' ? 'selected' : ''}>Document</option>
              <option value="code" ${artifact.type === 'code' ? 'selected' : ''}>Code</option>
              <option value="template" ${artifact.type === 'template' ? 'selected' : ''}>Template</option>
              <option value="specification" ${artifact.type === 'specification' ? 'selected' : ''}>Specification</option>
              <option value="other" ${artifact.type === 'other' ? 'selected' : ''}>Other</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-artifact-content">Content</label>
            <textarea id="edit-artifact-content" class="cc-input" rows="10" placeholder="Enter artifact content...">${artifact.content || ''}</textarea>
            <div class="form-help">Use Markdown formatting for rich text content</div>
          </div>
          <div class="form-group">
            <label for="edit-artifact-tags">Tags (comma-separated)</label>
            <input type="text" id="edit-artifact-tags" class="cc-input" placeholder="documentation, api, guide" value="${(artifact.tags || []).join(', ')}">
          </div>
          <div class="form-actions">
            <button type="button" class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
            <button type="submit" class="cc-button primary">Update Artifact</button>
          </div>
        </form>
      `);

      // Add form submit handler
      const form = modal.querySelector('#edit-artifact-form');
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await this.handleEditArtifact(artifactId, form, modal);
      });

    } catch (error) {
      console.error('[Sidebar] Failed to show edit artifact modal:', error);
      this.showNotification('Failed to load artifact for editing', 'error');
    }
  }

  async handleEditArtifact(artifactId, form, modal) {
    try {
      const tags = form.querySelector('#edit-artifact-tags').value
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const updates = {
        name: form.querySelector('#edit-artifact-name').value,
        description: form.querySelector('#edit-artifact-description').value,
        type: form.querySelector('#edit-artifact-type').value,
        content: form.querySelector('#edit-artifact-content').value,
        tags: tags
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Updating...';
      submitBtn.disabled = true;

      // Update artifact with version creation
      const updatedArtifact = await this.artifactManager.updateArtifact(artifactId, updates, {
        createVersion: true,
        changeSummary: 'Manual edit via sidebar'
      });

      // Close modal
      modal.remove();

      // Refresh artifacts list
      await this.loadTabData('artifacts');

      // Show success message
      this.showNotification(`Artifact "${updatedArtifact.name}" updated successfully!`, 'success');

    } catch (error) {
      console.error('[Sidebar] Failed to update artifact:', error);
      this.showNotification('Failed to update artifact. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Update Artifact';
      submitBtn.disabled = false;
    }
  }

  showCreateArtifactModal() {
    const modal = this.createModal('Create New Artifact', `
      <form id="create-artifact-form" class="modal-form">
        <div class="form-group">
          <label for="artifact-name">Artifact Name *</label>
          <input type="text" id="artifact-name" class="cc-input" required placeholder="Enter artifact name">
        </div>
        <div class="form-group">
          <label for="artifact-description">Description</label>
          <textarea id="artifact-description" class="cc-input" rows="3" placeholder="Describe this artifact..."></textarea>
        </div>
        <div class="form-group">
          <label for="artifact-type">Type</label>
          <select id="artifact-type" class="cc-input">
            <option value="document">Document</option>
            <option value="code">Code</option>
            <option value="template">Template</option>
            <option value="specification">Specification</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div class="form-group">
          <label for="artifact-content">Initial Content</label>
          <textarea id="artifact-content" class="cc-input" rows="8" placeholder="Enter initial content (optional)..."></textarea>
          <div class="form-help">You can continue editing this artifact after creation</div>
        </div>
        <div class="form-group">
          <label for="artifact-tags">Tags (comma-separated)</label>
          <input type="text" id="artifact-tags" class="cc-input" placeholder="documentation, api, guide">
        </div>
        <div class="form-actions">
          <button type="button" class="cc-button secondary" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
          <button type="submit" class="cc-button primary">Create Artifact</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-artifact-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleCreateArtifact(form, modal);
    });
  }

  async handleCreateArtifact(form, modal) {
    try {
      const tags = form.querySelector('#artifact-tags').value
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const artifactData = {
        name: form.querySelector('#artifact-name').value,
        description: form.querySelector('#artifact-description').value,
        type: form.querySelector('#artifact-type').value,
        content: form.querySelector('#artifact-content').value,
        tags: tags
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create artifact
      const artifact = await this.artifactManager.createArtifact(artifactData);

      // Close modal
      modal.remove();

      // Refresh artifacts list
      await this.loadTabData('artifacts');

      // Show success message
      this.showNotification('Artifact created successfully!', 'success');

    } catch (error) {
      console.error('[Sidebar] Failed to create artifact:', error);
      this.showNotification('Failed to create artifact. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Artifact';
      submitBtn.disabled = false;
    }
  }

  setupArtifactsEventListeners() {
    const newArtifactBtn = document.getElementById('new-artifact-btn');
    const refreshArtifactsBtn = document.getElementById('refresh-artifacts-btn');

    if (newArtifactBtn) {
      newArtifactBtn.addEventListener('click', () => {
        console.log('[Sidebar] New artifact button clicked');
        this.showCreateArtifactModal();
      });
    }

    if (refreshArtifactsBtn) {
      refreshArtifactsBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh artifacts button clicked');
        this.loadTabData('artifacts');
      });
    }
  }

  setupExportEventListeners() {
    const exportButtons = [
      'export-current-project-btn',
      'export-all-projects-btn',
      'export-current-artifact-btn',
      'export-templates-btn'
    ];

    exportButtons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', () => {
          const action = buttonId.replace('export-', '').replace('-btn', '');
          console.log(`[Sidebar] Export ${action} button clicked`);
          alert(`Export ${action} functionality will be implemented soon.`);
        });
      }
    });
  }
}

// Initialize sidebar when DOM is loaded
function initializeSidebar() {
  console.log('[Sidebar] Initializing sidebar manager...');
  console.log('[Sidebar] Document ready state:', document.readyState);
  console.log('[Sidebar] Available elements:', {
    'projects-tab': !!document.getElementById('projects-tab'),
    'personas-tab': !!document.getElementById('personas-tab'),
    'artifacts-tab': !!document.getElementById('artifacts-tab'),
    'export-tab': !!document.getElementById('export-tab')
  });

  window.sidebarManager = new SidebarManager();
  window.sidebarApp = window.sidebarManager; // Alias for easier access in onclick handlers
}

// Try multiple initialization methods
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeSidebar);
} else {
  // DOM is already loaded
  initializeSidebar();
}

// Also try after a short delay as fallback
setTimeout(() => {
  if (!window.sidebarManager) {
    console.log('[Sidebar] Fallback initialization...');
    initializeSidebar();
  }
}, 500);

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'SIDEBAR_ACTION') {
    switch (message.action) {
      case 'refresh':
        if (window.sidebarManager) {
          window.sidebarManager.refreshData();
        }
        break;
      case 'openTab':
        if (window.sidebarManager && message.tabId) {
          window.sidebarManager.openTab(message.tabId);
        }
        break;
      case 'edit':
        if (window.sidebarManager && message.itemType && message.itemId) {
          window.sidebarManager.handleEditFromContentScript(
            message.itemType,
            message.itemId,
            message.itemData
          );
        }
        break;
    }
    sendResponse({ success: true });
  }
});

// Listen for postMessage from content script
window.addEventListener('message', (event) => {
  console.log('[Sidebar] Received postMessage:', event.data);

  if (!window.sidebarManager) {
    console.log('[Sidebar] SidebarManager not ready yet');
    return;
  }

  switch (event.data.type) {
    case 'EDIT_ITEM':
      console.log('[Sidebar] Handling EDIT_ITEM message');
      window.sidebarManager.handleEditFromContentScript(
        event.data.itemType,
        event.data.itemId,
        event.data.itemData
      );
      break;

    case 'SWITCH_TAB':
      console.log('[Sidebar] Handling SWITCH_TAB message');
      if (event.data.tabId) {
        window.sidebarManager.switchTab(event.data.tabId);
      }
      break;

    default:
      console.log('[Sidebar] Unknown message type:', event.data.type);
  }
});

console.log('[Sidebar] Script loaded');