<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Clear Extension Storage</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 600px;
      margin: 50px auto;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
    }
    h1 {
      color: #dc2626;
      margin-bottom: 20px;
    }
    button {
      background: #dc2626;
      color: white;
      border: none;
      padding: 15px 30px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px;
    }
    button:hover {
      background: #b91c1c;
    }
    .success {
      background: #10b981;
    }
    .success:hover {
      background: #059669;
    }
    .results {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-top: 20px;
      font-family: monospace;
      text-align: left;
    }
    .warning {
      background: #fef3c7;
      border: 1px solid #f59e0b;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      color: #92400e;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🗑️ Clear Extension Storage</h1>
    
    <div class="warning">
      <strong>⚠️ Attention :</strong> Ceci va supprimer toutes les données de l'extension (projets, personas, artefacts). Les nouvelles données d'exemple seront créées automatiquement.
    </div>

    <button onclick="clearStorage()">Clear Storage</button>
    <button onclick="initializeSampleData()" class="success">Initialize Sample Data</button>
    <button onclick="checkStorage()">Check Current Data</button>

    <div id="results" class="results" style="display: none;"></div>
  </div>

  <script>
    function log(message, type = 'info') {
      const results = document.getElementById('results');
      results.style.display = 'block';
      const timestamp = new Date().toLocaleTimeString();
      const color = type === 'error' ? '#dc2626' : type === 'success' ? '#10b981' : '#374151';
      results.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
    }

    function clear() {
      document.getElementById('results').innerHTML = '';
    }

    async function clearStorage() {
      clear();
      log('Clearing extension storage...');

      if (typeof chrome === 'undefined' || !chrome.storage) {
        log('ERROR: Chrome storage not available. Make sure this is running in extension context.', 'error');
        return;
      }

      try {
        // Get all storage keys
        const allData = await new Promise((resolve) => {
          chrome.storage.local.get(null, resolve);
        });

        // Find all keys with cc_ prefix
        const ccKeys = Object.keys(allData).filter(key => key.startsWith('cc_'));
        
        if (ccKeys.length === 0) {
          log('No extension data found to clear.', 'success');
          return;
        }

        // Remove all cc_ keys
        await new Promise((resolve) => {
          chrome.storage.local.remove(ccKeys, resolve);
        });

        log(`SUCCESS: Cleared ${ccKeys.length} storage keys:`, 'success');
        ccKeys.forEach(key => log(`  - ${key}`));
        log('Extension storage cleared. Reload the extension to see new sample data.', 'success');

      } catch (error) {
        log(`ERROR: Failed to clear storage: ${error.message}`, 'error');
      }
    }

    async function initializeSampleData() {
      clear();
      log('Initializing sample data...');

      if (typeof chrome === 'undefined' || !chrome.storage) {
        log('ERROR: Chrome storage not available.', 'error');
        return;
      }

      try {
        const sampleProjects = [
          {
            id: 'proj_' + Date.now() + '_sample1',
            name: 'AI Assistant Development',
            title: 'AI Assistant Development',
            description: 'Building a comprehensive AI assistant for developers',
            slug: 'ai-assistant-development',
            status: 'active',
            priority: 'high',
            tags: ['ai', 'development', 'assistant'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastModified: '2 hours ago',
            artifactCount: 15
          },
          {
            id: 'proj_' + Date.now() + '_sample2',
            name: 'Code Review Tool',
            title: 'Code Review Tool',
            description: 'Automated code review and optimization suggestions',
            slug: 'code-review-tool',
            status: 'draft',
            priority: 'medium',
            tags: ['code-review', 'automation'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastModified: '1 day ago',
            artifactCount: 8
          }
        ];

        await new Promise((resolve) => {
          chrome.storage.local.set({ 'cc_projects': sampleProjects }, resolve);
        });

        log('SUCCESS: Sample projects created:', 'success');
        sampleProjects.forEach(project => {
          log(`  - ${project.name} (${project.status})`);
        });

      } catch (error) {
        log(`ERROR: Failed to initialize sample data: ${error.message}`, 'error');
      }
    }

    async function checkStorage() {
      clear();
      log('Checking current storage data...');

      if (typeof chrome === 'undefined' || !chrome.storage) {
        log('ERROR: Chrome storage not available.', 'error');
        return;
      }

      try {
        const allData = await new Promise((resolve) => {
          chrome.storage.local.get(null, resolve);
        });

        const ccKeys = Object.keys(allData).filter(key => key.startsWith('cc_'));
        
        if (ccKeys.length === 0) {
          log('No extension data found.', 'success');
          return;
        }

        log(`Found ${ccKeys.length} extension storage keys:`, 'success');
        
        ccKeys.forEach(key => {
          const data = allData[key];
          if (Array.isArray(data)) {
            log(`  - ${key}: ${data.length} items`);
            data.forEach((item, index) => {
              if (item.name || item.title) {
                log(`    ${index + 1}. ${item.name || item.title} (${item.status || 'no status'})`);
              }
            });
          } else {
            log(`  - ${key}: ${typeof data} (${JSON.stringify(data).substring(0, 100)}...)`);
          }
        });

      } catch (error) {
        log(`ERROR: Failed to check storage: ${error.message}`, 'error');
      }
    }
  </script>
</body>
</html>
